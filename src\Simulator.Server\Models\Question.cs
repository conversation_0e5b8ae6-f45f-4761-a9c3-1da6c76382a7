﻿using System.ComponentModel.DataAnnotations;

namespace Simulator.Server.Models;

public class Question
{
    [Key] public required string Id { get; init; }
    public required string Type { get; init; }
    public required string Title { get; init; }
    public required string Tags { get; init; }
    public required string Options { get; init; }
    public required string Answer { get; init; }
    public required double ErrorRate { get; init; }
    public required string Explain { get; init; }
}