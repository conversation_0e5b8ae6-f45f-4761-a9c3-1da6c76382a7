﻿<UserControl xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:icons="https://github.com/MahApps/IconPacks.Avalonia"
             xmlns:desktop="clr-namespace:Simulator.Desktop"
             xmlns:v="clr-namespace:Simulator.Desktop.Views"
             xmlns:components="clr-namespace:Simulator.Desktop.Components"
             mc:Ignorable="d" d:DesignWidth="800" d:DesignHeight="450"
             x:Class="Simulator.Desktop.Components.UserBar" VerticalAlignment="Top"
             NavigateCommand="{Binding $parent[desktop:MainWindow].NavigateCommand}">
    <Border Background="White" BorderBrush="Gray" BorderThickness="1" CornerRadius="999">
        <Grid ColumnDefinitions="Auto,Auto,*,Auto" Margin="15,0" ColumnSpacing="12">
            <TextBlock Grid.Column="0" Text="Examination" FontSize="16" FontWeight="Bold" VerticalAlignment="Center" />
            <TabStrip Grid.Column="1" Margin="8" x:DataType="TabStripItem" SelectedValueBinding="{Binding Tag}"
                      SelectedValue="{Binding $parent[components:UserBar].SelectedPage,Mode=OneWay}"
                      SelectionChanged="SelectingItemsControl_OnSelectionChanged">
                <TabStripItem Content="考试任务" Tag="{x:Type v:ExaminationContractView}" />
                <TabStripItem Content="自选考题" Tag="{x:Type v:ExaminationServiceView}" />
                <TabStripItem Content="题库列表" />
                <TabStripItem Content="历史记录" />
            </TabStrip>
            <StackPanel Grid.Column="3" Orientation="Horizontal" Spacing="12">
                <Border CornerRadius="999" BorderBrush="Gray" BorderThickness="1" VerticalAlignment="Center"
                        Padding="6">
                    <icons:PackIconLucide Kind="User" Height="14" Width="14" HorizontalAlignment="Center" />
                </Border>
                <TextBlock Text="学员 9527" VerticalAlignment="Center" />
                <DropDownButton>
                    <TextBlock Text="菜单" />
                    <DropDownButton.Flyout>
                        <MenuFlyout>
                            <MenuItem Header="退出" />
                        </MenuFlyout>
                    </DropDownButton.Flyout>
                </DropDownButton>
            </StackPanel>
        </Grid>
    </Border>
</UserControl>