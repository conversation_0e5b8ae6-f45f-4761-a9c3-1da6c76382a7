using Avalonia;
using Avalonia.Controls;
using Avalonia.Controls.ApplicationLifetimes;
using Avalonia.Markup.Xaml;
using Microsoft.Extensions.DependencyInjection;
using Simulator.Desktop.Services;

namespace Simulator.Desktop;

public partial class App : Application
{
    public override void Initialize()
    {
        AvaloniaXamlLoader.Load(this);
    }

    public override void OnFrameworkInitializationCompleted()
    {
        if (ApplicationLifetime is IClassicDesktopStyleApplicationLifetime desktop)
        {
            desktop.MainWindow = CreateWindow();
        }

        base.OnFrameworkInitializationCompleted();
    }

    private Window CreateWindow()
    {
        var provider = Program.AppHost!.Services;

        var navigation = provider.GetRequiredService<NavigationService>();


        var window = new MainWindow
        {
            NavigateHandler = navigation.Navigate,
            DataContext = ActivatorUtilities.CreateInstance<MainWindowContext>(provider)
        };

        navigation.NavigateHandler = window.Navigate;

        return window;
    }
}