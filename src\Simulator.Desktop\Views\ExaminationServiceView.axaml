﻿<controls:Page xmlns="https://github.com/avaloniaui"
               xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
               xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
               xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
               xmlns:controls="clr-namespace:Simulator.Desktop.Controls"
               xmlns:components="clr-namespace:Simulator.Desktop.Components"
               xmlns:v="clr-namespace:Simulator.Desktop.Views"
               mc:Ignorable="d" d:DesignWidth="800" d:DesignHeight="450"
               x:Class="Simulator.Desktop.Views.ExaminationServiceView">
    <controls:Page.Header>
        <components:UserBar SelectedPage="{x:Type v:ExaminationServiceView}" />
    </controls:Page.Header>
</controls:Page>