<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AvaloniaProject">
    <option name="projectPerEditor">
      <map>
        <entry key="src/Simulator.Desktop/App.axaml" value="src/Simulator.Desktop/Simulator.Desktop.csproj" />
        <entry key="src/Simulator.Desktop/Components/UserBar.axaml" value="src/Simulator.Desktop/Simulator.Desktop.csproj" />
        <entry key="src/Simulator.Desktop/Conponents/UserBar.axaml" value="src/Simulator.Desktop/Simulator.Desktop.csproj" />
        <entry key="src/Simulator.Desktop/Controls/Page.axaml" value="src/Simulator.Desktop/Simulator.Desktop.csproj" />
        <entry key="src/Simulator.Desktop/MainWindow.axaml" value="src/Simulator.Desktop/Simulator.Desktop.csproj" />
        <entry key="src/Simulator.Desktop/Themes/Controls.axaml" value="src/Simulator.Desktop/Simulator.Desktop.csproj" />
        <entry key="src/Simulator.Desktop/Views/Administration.axaml" value="src/Simulator.Desktop/Simulator.Desktop.csproj" />
        <entry key="src/Simulator.Desktop/Views/AdministrationView.axaml" value="src/Simulator.Desktop/Simulator.Desktop.csproj" />
        <entry key="src/Simulator.Desktop/Views/Examination.axaml" value="src/Simulator.Desktop/Simulator.Desktop.csproj" />
        <entry key="src/Simulator.Desktop/Views/ExaminationContractView.axaml" value="src/Simulator.Desktop/Simulator.Desktop.csproj" />
        <entry key="src/Simulator.Desktop/Views/ExaminationLoginView.axaml" value="src/Simulator.Desktop/Simulator.Desktop.csproj" />
        <entry key="src/Simulator.Desktop/Views/ExaminationPortalView.axaml" value="src/Simulator.Desktop/Simulator.Desktop.csproj" />
        <entry key="src/Simulator.Desktop/Views/ExaminationServiceView.axaml" value="src/Simulator.Desktop/Simulator.Desktop.csproj" />
        <entry key="src/Simulator.Desktop/Views/ExaminationView.axaml" value="src/Simulator.Desktop/Simulator.Desktop.csproj" />
        <entry key="src/Simulator.Desktop/Views/PortalView.axaml" value="src/Simulator.Desktop/Simulator.Desktop.csproj" />
        <entry key="src/Simulator.Desktop/Views/SimulationOverviewView.axaml" value="src/Simulator.Desktop/Simulator.Desktop.csproj" />
        <entry key="src/Simulator.Desktop/Views/SimulationView.axaml" value="src/Simulator.Desktop/Simulator.Desktop.csproj" />
      </map>
    </option>
  </component>
</project>