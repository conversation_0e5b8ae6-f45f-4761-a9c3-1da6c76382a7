using System.Security.Claims;
using Simulator.Server.Models;

namespace Simulator.Server.Services;

public interface IJwtService
{
    /// <summary>
    /// 为用户生成JWT令牌
    /// </summary>
    /// <param name="user">用户信息</param>
    /// <returns>JWT令牌字符串</returns>
    string GenerateToken(User user);
    
    /// <summary>
    /// 验证JWT令牌
    /// </summary>
    /// <param name="token">JWT令牌</param>
    /// <returns>验证成功返回ClaimsPrincipal，失败返回null</returns>
    ClaimsPrincipal? ValidateToken(string token);
    
    /// <summary>
    /// 从令牌中获取用户ID
    /// </summary>
    /// <param name="token">JWT令牌</param>
    /// <returns>用户ID，失败返回null</returns>
    string? GetUserIdFromToken(string token);
}
