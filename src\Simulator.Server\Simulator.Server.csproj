<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UserSecretsId>aspnet-Simulator.Server-9c23796f-4445-482c-8f27-ae137c66eb56</UserSecretsId>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="FreeSql" Version="3.5.209" />
    <PackageReference Include="FreeSql.Provider.Sqlite" Version="3.5.209" />
    <PackageReference Include="Mapster" Version="7.4.0" />
    <PackageReference Include="Mapster.DependencyInjection" Version="1.0.1" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="9.0.6" NoWarn="NU1605" />
    <PackageReference Include="Nanoid" Version="3.1.0" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="9.0.1" />
    <PackageReference Include="SystemTextJsonPatch" Version="4.2.0" />
  </ItemGroup>

</Project>
