﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Simulator.Desktop.Services;

namespace Simulator.Desktop;

public static class Startup
{
    public static void ConfigureServices(IServiceCollection services, IConfiguration configuration,
        IHostEnvironment environment)
    {
        services.AddHostedService<AvaloniaLifetime>();

        services.AddSingleton<NavigationService>();
    }
}