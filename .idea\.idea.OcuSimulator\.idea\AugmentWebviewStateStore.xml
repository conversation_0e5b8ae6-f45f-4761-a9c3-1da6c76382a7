<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AugmentWebviewStateStore">
    <option name="stateMap">
      <map>
        <entry key="CHAT_STATE" value="{&quot;currentConversationId&quot;:&quot;677c9904-f78b-41ef-8457-fc4b3698935e&quot;,&quot;conversations&quot;:{&quot;89a586fb-333e-4a49-9153-39e31199427f&quot;:{&quot;id&quot;:&quot;89a586fb-333e-4a49-9153-39e31199427f&quot;,&quot;createdAtIso&quot;:&quot;2025-07-08T05:53:41.592Z&quot;,&quot;lastInteractedAtIso&quot;:&quot;2025-07-08T05:53:41.592Z&quot;,&quot;chatHistory&quot;:[],&quot;feedbackStates&quot;:{},&quot;toolUseStates&quot;:{},&quot;draftExchange&quot;:{&quot;request_message&quot;:&quot;&quot;,&quot;rich_text_json_repr&quot;:{&quot;type&quot;:&quot;doc&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;paragraph&quot;}]},&quot;mentioned_items&quot;:[],&quot;status&quot;:&quot;draft&quot;},&quot;requestIds&quot;:[],&quot;isPinned&quot;:false,&quot;isShareable&quot;:false,&quot;extraData&quot;:{&quot;hasDirtyEdits&quot;:false},&quot;personaType&quot;:0,&quot;rootTaskUuid&quot;:&quot;579792f9-c566-4da0-ad5a-a971a7f88baa&quot;},&quot;677c9904-f78b-41ef-8457-fc4b3698935e&quot;:{&quot;id&quot;:&quot;677c9904-f78b-41ef-8457-fc4b3698935e&quot;,&quot;createdAtIso&quot;:&quot;2025-07-08T06:23:10.861Z&quot;,&quot;lastInteractedAtIso&quot;:&quot;2025-07-08T06:58:41.497Z&quot;,&quot;chatHistory&quot;:[{&quot;request_id&quot;:&quot;19fdc1d1-68e6-4348-a754-7e2f6d5cdd18&quot;,&quot;uuid&quot;:&quot;1dc390dd-aee6-4bd8-a123-fc15439b5e69&quot;,&quot;chatItemType&quot;:&quot;agentic-checkpoint-delimiter&quot;,&quot;status&quot;:&quot;success&quot;,&quot;fromTimestamp&quot;:0,&quot;toTimestamp&quot;:1751955790863,&quot;seen_state&quot;:&quot;seen&quot;},{&quot;status&quot;:&quot;success&quot;,&quot;request_id&quot;:&quot;0d763763-0075-4f4e-a178-c306937c58ac&quot;,&quot;request_message&quot;:&quot;这里的ExecuteIdentity和普通的Execute有什么区别，标记Column(Identity=True)作用是什么&quot;,&quot;rich_text_json_repr&quot;:{&quot;type&quot;:&quot;doc&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;paragraph&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;这里的ExecuteIdentity和普通的Execute有什么区别，标记Column(Identity=True)作用是什么&quot;}]}]},&quot;mentioned_items&quot;:[],&quot;structured_output_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:5,&quot;content&quot;:&quot;&quot;,&quot;tool_use&quot;:{&quot;tool_use_id&quot;:&quot;toolu_01ABhGDpc7xp386iqWAnvikm&quot;,&quot;tool_name&quot;:&quot;view&quot;,&quot;input_json&quot;:&quot;{\&quot;path\&quot;: \&quot;src/Simulator.Server/Controllers/UserController.cs\&quot;, \&quot;type\&quot;: \&quot;file\&quot;}&quot;,&quot;mcp_server_name&quot;:&quot;&quot;,&quot;mcp_tool_name&quot;:&quot;&quot;}},{&quot;id&quot;:0,&quot;type&quot;:0,&quot;content&quot;:&quot;I'll help you understand the difference between `ExecuteIdentity` and regular `Execute` methods, and explain the purpose of the `Column(Identity=True)` attribute. Let me first examine the current code to provide you with accurate information.\n&quot;},{&quot;id&quot;:2,&quot;type&quot;:2,&quot;content&quot;:&quot;&quot;},{&quot;id&quot;:3,&quot;type&quot;:3,&quot;content&quot;:&quot;&quot;}],&quot;seen_state&quot;:&quot;seen&quot;,&quot;structured_request_nodes&quot;:[{&quot;id&quot;:0,&quot;type&quot;:0,&quot;text_node&quot;:{&quot;content&quot;:&quot;这里的ExecuteIdentity和普通的Execute有什么区别，标记Column(Identity=True)作用是什么&quot;}},{&quot;id&quot;:1,&quot;type&quot;:4,&quot;ide_state_node&quot;:{&quot;workspaceFolders&quot;:[{&quot;repositoryRoot&quot;:&quot;C:/Users/<USER>/Projects/OcuSimulator&quot;,&quot;folderRoot&quot;:&quot;C:/Users/<USER>/Projects/OcuSimulator&quot;}],&quot;workspaceFoldersUnchanged&quot;:false,&quot;currentTerminal&quot;:{&quot;terminalId&quot;:19,&quot;currentWorkingDirectory&quot;:&quot;C:/Users/<USER>/Projects/OcuSimulator&quot;}}}],&quot;timestamp&quot;:&quot;2025-07-08T06:58:08.587Z&quot;,&quot;response_text&quot;:&quot;I'll help you understand the difference between `ExecuteIdentity` and regular `Execute` methods, and explain the purpose of the `Column(Identity=True)` attribute. Let me first examine the current code to provide you with accurate information.\n&quot;,&quot;workspace_file_chunks&quot;:[]},{&quot;status&quot;:&quot;success&quot;,&quot;request_id&quot;:&quot;dd5161ee-0eef-4c9b-b08b-3b01b6ff2391&quot;,&quot;request_message&quot;:&quot;&quot;,&quot;structured_output_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:5,&quot;content&quot;:&quot;&quot;,&quot;tool_use&quot;:{&quot;tool_use_id&quot;:&quot;toolu_0146tG1aZwyfYpzxTFSEF74b&quot;,&quot;tool_name&quot;:&quot;view&quot;,&quot;input_json&quot;:&quot;{\&quot;path\&quot;: \&quot;src/Simulator.Server/Models/User.cs\&quot;, \&quot;type\&quot;: \&quot;file\&quot;}&quot;,&quot;mcp_server_name&quot;:&quot;&quot;,&quot;mcp_tool_name&quot;:&quot;&quot;}},{&quot;id&quot;:0,&quot;type&quot;:0,&quot;content&quot;:&quot;&quot;},{&quot;id&quot;:2,&quot;type&quot;:2,&quot;content&quot;:&quot;&quot;},{&quot;id&quot;:3,&quot;type&quot;:3,&quot;content&quot;:&quot;&quot;}],&quot;seen_state&quot;:&quot;seen&quot;,&quot;structured_request_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:1,&quot;tool_result_node&quot;:{&quot;content&quot;:&quot;Note: Path was automatically corrected from 'src/Simulator.Server/Controllers/UserController.cs' to 'C:\\Users\\<USER>\\Projects\\OcuSimulator\\src\\Simulator.Server\\Controllers\\UserController.cs'.\n\nHere's the result of running `cat -n` on C:\\Users\\<USER>\\Projects\\OcuSimulator\\src\\Simulator.Server\\Controllers\\UserController.cs:\n     1\t﻿using Microsoft.AspNetCore.Mvc;\n     2\tusing NanoidDotNet;\n     3\tusing Simulator.Server.Models;\n     4\t\n     5\tnamespace Simulator.Server.Controllers;\n     6\t\n     7\t[ApiController]\n     8\t[Route(\&quot;api/[controller]\&quot;)]\n     9\tpublic class UserController(IFreeSql sql) : Controller\n    10\t{\n    11\t    [HttpPost(\&quot;login\&quot;)]\n    12\t    public IActionResult Login(string username, string password)\n    13\t    {\n    14\t        var user = sql.Select&lt;User&gt;().Where(u =&gt; u.Username == username &amp;&amp; u.Password == password).ToOne();\n    15\t        if (user == null)\n    16\t        {\n    17\t            return Unauthorized();\n    18\t        }\n    19\t\n    20\t        return Ok(user);\n    21\t    }\n    22\t\n    23\t    public IActionResult Register(string username, string password, bool isTeacher)\n    24\t    {\n    25\t        var astudentbutmaybeateacher = new User\n    26\t        {\n    27\t            Id = Nanoid.Generate(),\n    28\t            Username = username,\n    29\t            Password = password,\n    30\t            IsTeacher = isTeacher\n    31\t        };\n    32\t\n    33\t        sql.Insert(astudentbutmaybeateacher).ExecuteIdentity();\n    34\t\n    35\t        return Ok(astudentbutmaybeateacher);\n    36\t    }\n    37\t}\nTotal lines in file: 37\n&quot;,&quot;is_error&quot;:false,&quot;request_id&quot;:&quot;&quot;,&quot;tool_use_id&quot;:&quot;toolu_01ABhGDpc7xp386iqWAnvikm&quot;}},{&quot;id&quot;:2,&quot;type&quot;:4,&quot;ide_state_node&quot;:{&quot;workspaceFolders&quot;:[{&quot;repositoryRoot&quot;:&quot;C:/Users/<USER>/Projects/OcuSimulator&quot;,&quot;folderRoot&quot;:&quot;C:/Users/<USER>/Projects/OcuSimulator&quot;}],&quot;workspaceFoldersUnchanged&quot;:false,&quot;currentTerminal&quot;:{&quot;terminalId&quot;:19,&quot;currentWorkingDirectory&quot;:&quot;C:/Users/<USER>/Projects/OcuSimulator&quot;}}}],&quot;timestamp&quot;:&quot;2025-07-08T06:58:13.911Z&quot;,&quot;response_text&quot;:&quot;&quot;,&quot;workspace_file_chunks&quot;:[]},{&quot;status&quot;:&quot;success&quot;,&quot;request_id&quot;:&quot;ac885dd9-587d-44cf-932d-62be2644bf64&quot;,&quot;request_message&quot;:&quot;&quot;,&quot;structured_output_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:5,&quot;content&quot;:&quot;&quot;,&quot;tool_use&quot;:{&quot;tool_use_id&quot;:&quot;toolu_01NBbDSn4Dk6nnwyjq3ESvPc&quot;,&quot;tool_name&quot;:&quot;codebase-retrieval&quot;,&quot;input_json&quot;:&quot;{\&quot;information_request\&quot;: \&quot;FreeSql ExecuteIdentity vs Execute methods, Column Identity attribute usage, database identity columns in FreeSql\&quot;}&quot;,&quot;mcp_server_name&quot;:&quot;&quot;,&quot;mcp_tool_name&quot;:&quot;&quot;}},{&quot;id&quot;:0,&quot;type&quot;:0,&quot;content&quot;:&quot;&quot;},{&quot;id&quot;:2,&quot;type&quot;:2,&quot;content&quot;:&quot;&quot;},{&quot;id&quot;:3,&quot;type&quot;:3,&quot;content&quot;:&quot;&quot;}],&quot;seen_state&quot;:&quot;seen&quot;,&quot;structured_request_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:1,&quot;tool_result_node&quot;:{&quot;content&quot;:&quot;Note: Path was automatically corrected from 'src/Simulator.Server/Models/User.cs' to 'C:\\Users\\<USER>\\Projects\\OcuSimulator\\src\\Simulator.Server\\Models\\User.cs'.\n\nHere's the result of running `cat -n` on C:\\Users\\<USER>\\Projects\\OcuSimulator\\src\\Simulator.Server\\Models\\User.cs:\n     1\t﻿using System.ComponentModel.DataAnnotations;\n     2\t\n     3\tnamespace Simulator.Server.Models;\n     4\t\n     5\tpublic class User\n     6\t{\n     7\t    [Key] public required string Id { get; init; }\n     8\t    public required string Username { get; init; }\n     9\t    public required string Password { get; init; }\n    10\t    public required bool IsTeacher { get; init; }\n    11\t}\nTotal lines in file: 11\n&quot;,&quot;is_error&quot;:false,&quot;request_id&quot;:&quot;&quot;,&quot;tool_use_id&quot;:&quot;toolu_0146tG1aZwyfYpzxTFSEF74b&quot;}},{&quot;id&quot;:2,&quot;type&quot;:4,&quot;ide_state_node&quot;:{&quot;workspaceFolders&quot;:[{&quot;repositoryRoot&quot;:&quot;C:/Users/<USER>/Projects/OcuSimulator&quot;,&quot;folderRoot&quot;:&quot;C:/Users/<USER>/Projects/OcuSimulator&quot;}],&quot;workspaceFoldersUnchanged&quot;:false,&quot;currentTerminal&quot;:{&quot;terminalId&quot;:19,&quot;currentWorkingDirectory&quot;:&quot;C:/Users/<USER>/Projects/OcuSimulator&quot;}}}],&quot;timestamp&quot;:&quot;2025-07-08T06:58:17.303Z&quot;,&quot;response_text&quot;:&quot;&quot;,&quot;workspace_file_chunks&quot;:[]},{&quot;status&quot;:&quot;success&quot;,&quot;request_id&quot;:&quot;c2690f7a-955f-4a79-a0f3-4baa6723d422&quot;,&quot;request_message&quot;:&quot;&quot;,&quot;structured_output_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:5,&quot;content&quot;:&quot;&quot;,&quot;tool_use&quot;:{&quot;tool_use_id&quot;:&quot;toolu_01DN1FtLCx4nddUkhSnJzZFS&quot;,&quot;tool_name&quot;:&quot;web-search&quot;,&quot;input_json&quot;:&quot;{\&quot;query\&quot;: \&quot;FreeSql ExecuteIdentity vs Execute difference Column Identity attribute\&quot;}&quot;,&quot;mcp_server_name&quot;:&quot;&quot;,&quot;mcp_tool_name&quot;:&quot;&quot;}},{&quot;id&quot;:0,&quot;type&quot;:0,&quot;content&quot;:&quot;现在让我搜索更多关于FreeSql中ExecuteIdentity和Column Identity的具体信息：\n&quot;},{&quot;id&quot;:2,&quot;type&quot;:2,&quot;content&quot;:&quot;&quot;},{&quot;id&quot;:3,&quot;type&quot;:3,&quot;content&quot;:&quot;&quot;}],&quot;seen_state&quot;:&quot;seen&quot;,&quot;structured_request_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:1,&quot;tool_result_node&quot;:{&quot;content&quot;:&quot;The following code sections were retrieved:\nPath: docs/README_FreeSql.md\n     1\t# FreeSql 集成说明\n     2\t\n     3\t本项目已成功集成 FreeSql 并配置使用 SQLite 数据库。\n     4\t\n     5\t## 已安装的包\n     6\t\n     7\t- `FreeSql` - FreeSql 核心包\n     8\t- `FreeSql.Provider.Sqlite` - SQLite 数据库提供程序\n     9\t\n    10\t## 项目结构\n    11\t\n    12\t```\n    13\tsrc/Simulator.Server/\n    14\t├── Data/\n    15\t│   ├── FreeSqlExtensions.cs         # FreeSql 配置和扩展方法\n    16\t│   └── Question.cs                  # 其他实体模型\n    17\t├── Models/\n    18\t│   └── User.cs                      # 示例实体模型\n    19\t├── Controllers/\n    20\t│   └── UsersController.cs           # 示例 API 控制器\n    21\t├── appsettings.json                 # 生产环境配置\n    22\t├── appsettings.Development.json     # 开发环境配置\n    23\t└── simulator_dev.db                 # SQLite 数据库文件（开发环境）\n    24\t```\n    25\t\n    26\t## 数据库配置\n    27\t\n    28\t### 连接字符串\n    29\t\n    30\t- **开发环境**: `Data Source=simulator_dev.db`\n    31\t- **生产环境**: `Data Source=simulator.db`\n    32\t\n    33\t### FreeSql 配置\n    34\t\n    35\tFreeSql 配置位于 `Data/FreeSqlExtensions.cs`，包含：\n    36\t- 数据库连接配置\n    37\t- 自动同步结构设置\n    38\t- SQL 监控配置\n    39\t\n    40\t## 使用示例\n    41\t\n    42\t### 1. 添加新实体\n    43\t\n    44\t1. 在 `Models/` 目录下创建实体类\n    45\t2. 使用 FreeSql 注解配置实体（如 `[Table]`, `[Column]` 等）\n    46\t3. 在 `FreeSqlExtensions.cs` 的 `EnsureDatabaseCreatedAsync` 方法中添加 `freeSql.CodeFirst.SyncStructure&lt;YourEntity&gt;()`\n    47\t4. FreeSql 会自动创建或更新数据库结构\n    48\t\n    49\t### 2. 在控制器中使用\n    50\t\n    51\t```csharp\n    52\t[ApiController]\n    53\t[Route(\&quot;api/[controller]\&quot;)]\n    54\tpublic class YourController : ControllerBase\n    55\t{\n    56\t    private readonly IFreeSql _freeSql;\n    57\t\n    58\t    public YourController(IFreeSql freeSql)\n    59\t    {\n    60\t        _freeSql = freeSql;\n    61\t    }\n    62\t\n    63\t    [HttpGet]\n    64\t    public async Task&lt;ActionResult&lt;IEnumerable&lt;YourEntity&gt;&gt;&gt; Get()\n    65\t    {\n    66\t        var entities = await _freeSql.Select&lt;YourEntity&gt;().ToListAsync();\n    67\t        return Ok(entities);\n    68\t    }\n    69\t}\n    70\t```\n    71\t\n    72\t### 3. 在服务中使用\n    73\t\n    74\t```csharp\n    75\tpublic class YourService\n    76\t{\n    77\t    private readonly IFreeSql _freeSql;\n    78\t\n    79\t    public YourService(IFreeSql freeSql)\n    80\t    {\n    81\t        _freeSql = freeSql;\n    82\t    }\n    83\t\n    84\t    public async Task&lt;List&lt;YourEntity&gt;&gt; GetAllAsync()\n    85\t    {\n    86\t        return await _freeSql.Select&lt;YourEntity&gt;().ToListAsync();\n    87\t    }\n    88\t}\n    89\t```\n    90\t\n    91\t## FreeSql 常用操作\n    92\t\n    93\t```csharp\n    94\t// 查询\n    95\tvar users = await freeSql.Select&lt;User&gt;().ToListAsync();\n    96\tvar user = await freeSql.Select&lt;User&gt;().Where(u =&gt; u.Id == 1).FirstAsync();\n    97\t\n    98\t// 插入\n    99\tvar newUser = new User { Name = \&quot;张三\&quot;, Email = \&quot;<EMAIL>\&quot; };\n   100\tawait freeSql.Insert(newUser).ExecuteAffrowsAsync();\n   101\t\n   102\t// 更新\n   103\tawait freeSql.Update&lt;User&gt;()\n   104\t    .Set(u =&gt; u.Name, \&quot;李四\&quot;)\n   105\t    .Where(u =&gt; u.Id == 1)\n   106\t    .ExecuteAffrowsAsync();\n   107\t\n   108\t// 删除\n   109\tawait freeSql.Delete&lt;User&gt;().Where(u =&gt; u.Id == 1).ExecuteAffrowsAsync();\n   110\t\n   111\t// 同步数据库结构\n   112\tfreeSql.CodeFirst.SyncStructure&lt;User&gt;();\n   113\t```\n   114\t\n   115\t## API 端点示例\n   116\t\n   117\t应用程序包含一个示例 Users API：\n   118\t\n   119\t- `GET /api/users` - 获取所有用户\n   120\t- `GET /api/users/{id}` - 获取指定用户\n   121\t- `POST /api/users` - 创建新用户\n   122\t- `PUT /api/users/{id}` - 更新用户\n   123\t- `DELETE /api/users/{id}` - 删除用户\n   124\t\n   125\t## 数据库初始化\n   126\t\n   127\t应用程序启动时会自动：\n   128\t1. 同步数据库结构（创建或更新表）\n   129\t2. 在开发环境中添加示例数据（如果数据库为空）\n   130\t\n   131\t这通过 `Program.cs` 中的以下代码实现：\n   132\t\n   133\t```csharp\n   134\tawait app.EnsureDatabaseCreatedAsync();\n   135\tif (app.Environment.IsDevelopment())\n   136\t{\n   137\t    await app.SeedDatabaseAsync();\n   138\t}\n   139\t```\n   140\t\n   141\t## 注意事项\n   142\t\n   143\t1. SQLite 数据库文件会在项目根目录下创建\n   144\t2. 开发环境和生产环境使用不同的数据库文件\n   145\t3. 迁移文件应该提交到版本控制系统\n   146\t4. 数据库文件可以添加到 `.gitignore` 中（如果不需要共享数据）\n...\nPath: src/Simulator.Server/Data/FreeSqlExtensions.cs\n     1\tusing FreeSql;\n     2\tusing Simulator.Server.Models;\n     3\t\n     4\tnamespace Simulator.Server.Data\n     5\t{\n     6\t    public static class FreeSqlExtensions\n     7\t    {\n     8\t        public static IServiceCollection AddFreeSql(this IServiceCollection services, IConfiguration configuration)\n     9\t        {\n    10\t            services.AddSingleton&lt;IFreeSql&gt;(_ =&gt; new FreeSqlBuilder()\n    11\t                .UseConnectionString(DataType.Sqlite, \&quot;Data Source=Simulator.db;Cache=Private\&quot;)\n    12\t                .UseAutoSyncStructure(true)\n    13\t                .Build());\n    14\t\n    15\t\n    16\t            return services;\n    17\t        }\n    18\t    }\n    19\t}...\nPath: src/Simulator.Server/README_EFCore.md\n     1\t# Entity Framework Core 集成说明\n     2\t\n     3\t本项目已成功集成 Entity Framework Core 并配置使用 SQLite 数据库。\n     4\t\n     5\t## 已安装的包\n     6\t\n     7\t- `Microsoft.EntityFrameworkCore.Sqlite` - SQLite 数据库提供程序\n     8\t- `Microsoft.EntityFrameworkCore.Tools` - EF Core 工具（用于迁移等）\n     9\t\n    10\t## 项目结构\n    11\t\n    12\t```\n    13\tsrc/Simulator.Server/\n    14\t├── Data/\n    15\t│   ├── ApplicationDbContext.cs      # 数据库上下文\n    16\t│   └── DatabaseExtensions.cs       # 数据库扩展方法\n    17\t├── Models/\n    18\t│   └── User.cs                      # 示例实体模型\n    19\t├── Controllers/\n    20\t│   └── UsersController.cs           # 示例 API 控制器\n    21\t├── Migrations/                      # EF Core 迁移文件\n    22\t├── appsettings.json                 # 生产环境配置\n    23\t├── appsettings.Development.json     # 开发环境配置\n    24\t└── simulator_dev.db                 # SQLite 数据库文件（开发环境）\n    25\t```\n    26\t\n    27\t## 数据库配置\n    28\t\n    29\t### 连接字符串\n    30\t\n    31\t- **开发环境**: `Data Source=simulator_dev.db`\n    32\t- **生产环境**: `Data Source=simulator.db`\n    33\t\n    34\t### 数据库上下文\n    35\t\n    36\t`ApplicationDbContext` 类位于 `Data/ApplicationDbContext.cs`，包含：\n    37\t- 实体配置\n    38\t- 关系映射\n    39\t- 约束定义\n    40\t\n    41\t## 使用示例\n    42\t\n    43\t### 1. 添加新实体\n    44\t\n    45\t1. 在 `Models/` 目录下创建实体类\n    46\t2. 在 `ApplicationDbContext` 中添加 `DbSet&lt;T&gt;` 属性\n    47\t3. 在 `OnModelCreating` 方法中配置实体关系\n    48\t4. 创建迁移：`dotnet ef migrations add &lt;MigrationName&gt;`\n    49\t5. 应用迁移：`dotnet ef database update`\n    50\t\n    51\t### 2. 在控制器中使用\n    52\t\n    53\t```csharp\n    54\t[ApiController]\n    55\t[Route(\&quot;api/[controller]\&quot;)]\n    56\tpublic class YourController : ControllerBase\n    57\t{\n    58\t    private readonly ApplicationDbContext _context;\n    59\t\n    60\t    public YourController(ApplicationDbContext context)\n    61\t    {\n    62\t        _context = context;\n    63\t    }\n    64\t\n    65\t    [HttpGet]\n    66\t    public async Task&lt;ActionResult&lt;IEnumerable&lt;YourEntity&gt;&gt;&gt; Get()\n    67\t    {\n    68\t        return await _context.YourEntities.ToListAsync();\n    69\t    }\n    70\t}\n...\n    90\t```\n    91\t\n    92\t## 常用 EF Core 命令\n    93\t\n    94\t```bash\n    95\t# 创建迁移\n    96\tdotnet ef migrations add &lt;MigrationName&gt;\n    97\t\n    98\t# 应用迁移\n    99\tdotnet ef database update\n   100\t\n   101\t# 回滚到指定迁移\n   102\tdotnet ef database update &lt;MigrationName&gt;\n   103\t\n   104\t# 删除最后一个迁移\n   105\tdotnet ef migrations remove\n   106\t\n   107\t# 查看迁移状态\n   108\tdotnet ef migrations list\n   109\t\n   110\t# 生成 SQL 脚本\n   111\tdotnet ef migrations script\n   112\t```\n   113\t\n   114\t## API 端点示例\n   115\t\n   116\t应用程序包含一个示例 Users API：\n   117\t\n   118\t- `GET /api/users` - 获取所有用户\n   119\t- `GET /api/users/{id}` - 获取指定用户\n   120\t- `POST /api/users` - 创建新用户\n   121\t- `PUT /api/users/{id}` - 更新用户\n   122\t- `DELETE /api/users/{id}` - 删除用户\n   123\t\n   124\t## 数据库初始化\n   125\t\n   126\t应用程序启动时会自动：\n   127\t1. 应用所有待处理的迁移\n   128\t2. 在开发环境中添加示例数据（如果数据库为空）\n   129\t\n   130\t这通过 `Program.cs` 中的以下代码实现：\n   131\t\n   132\t```csharp\n   133\tawait app.EnsureDatabaseCreatedAsync();\n   134\tif (app.Environment.IsDevelopment())\n   135\t{\n   136\t    await app.SeedDatabaseAsync();\n   137\t}\n...\nPath: src/Simulator.Server/Startup.cs\n     1\tusing FreeSql;\n     2\tusing Mapster;\n     3\t\n     4\tnamespace Simulator.Server\n     5\t{\n     6\t    public static class Startup\n     7\t    {\n     8\t        public static void ConfigureServices(IServiceCollection services, IConfiguration configuration)\n     9\t        {\n    10\t            services.AddSingleton&lt;IFreeSql&gt;(_ =&gt; new FreeSqlBuilder()\n    11\t                .UseAdoConnectionPool(true)\n    12\t                .UseConnectionString(DataType.Sqlite, \&quot;Data Source=Simulator.db;Cache=Private\&quot;)\n    13\t                .UseAutoSyncStructure(true)\n    14\t                .Build());\n    15\t\n    16\t            services.AddMapster();\n    17\t        }\n    18\t    }\n    19\t}...\nPath: src/Simulator.Server/Migrations/20250707083248_InitialCreate.cs\n...\n     5\t\n     6\tnamespace Simulator.Server.Migrations\n     7\t{\n     8\t    /// &lt;inheritdoc /&gt;\n     9\t    public partial class InitialCreate : Migration\n    10\t    {\n    11\t        /// &lt;inheritdoc /&gt;\n    12\t        protected override void Up(MigrationBuilder migrationBuilder)\n    13\t        {\n    14\t            migrationBuilder.CreateTable(\n    15\t                name: \&quot;Users\&quot;,\n    16\t                columns: table =&gt; new\n    17\t                {\n    18\t                    Id = table.Column&lt;int&gt;(type: \&quot;INTEGER\&quot;, nullable: false)\n    19\t                        .Annotation(\&quot;Sqlite:Autoincrement\&quot;, true),\n    20\t                    Name = table.Column&lt;string&gt;(type: \&quot;TEXT\&quot;, maxLength: 100, nullable: false),\n    21\t                    Email = table.Column&lt;string&gt;(type: \&quot;TEXT\&quot;, maxLength: 255, nullable: false),\n    22\t                    CreatedAt = table.Column&lt;DateTime&gt;(type: \&quot;TEXT\&quot;, nullable: false, defaultValueSql: \&quot;datetime('now')\&quot;),\n    23\t                    UpdatedAt = table.Column&lt;DateTime&gt;(type: \&quot;TEXT\&quot;, nullable: true)\n    24\t                },\n    25\t                constraints: table =&gt;\n    26\t                {\n    27\t                    table.PrimaryKey(\&quot;PK_Users\&quot;, x =&gt; x.Id);\n    28\t                });\n...\nPath: src/Simulator.Server/Program.cs\n     1\tusing Microsoft.AspNetCore.Authentication.JwtBearer;\n     2\tusing Microsoft.IdentityModel.Tokens;\n     3\tusing Simulator.Server;\n     4\t\n     5\tvar builder = WebApplication.CreateBuilder(args);\n     6\t\n     7\t// Add services to the container.\n     8\tbuilder.Services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme);\n...\nPath: src/Simulator.Server/Models/User.cs\n     1\tusing System.ComponentModel.DataAnnotations;\n     2\t\n     3\tnamespace Simulator.Server.Models;\n     4\t\n     5\tpublic class User\n     6\t{\n     7\t    [Key] public required string Id { get; init; }\n     8\t    public required string Username { get; init; }\n     9\t    public required string Password { get; init; }\n    10\t    public required bool IsTeacher { get; init; }\n    11\t}...\nPath: src/Simulator.Desktop/app.manifest\n     1\t&lt;?xml version=\&quot;1.0\&quot; encoding=\&quot;utf-8\&quot;?&gt;\n     2\t&lt;assembly manifestVersion=\&quot;1.0\&quot; xmlns=\&quot;urn:schemas-microsoft-com:asm.v1\&quot;&gt;\n     3\t  &lt;!-- This manifest is used on Windows only.\n     4\t       Don't remove it as it might cause problems with window transparency and embedded controls.\n     5\t       For more details visit https://learn.microsoft.com/en-us/windows/win32/sbscs/application-manifests --&gt;\n     6\t  &lt;assemblyIdentity version=\&quot;1.0.0.0\&quot; name=\&quot;Simulator.Desktop.Desktop\&quot;/&gt;\n...\nPath: src/Simulator.Server/Migrations/ApplicationDbContextModelSnapshot.cs\n...\n    19\t\n    20\t            modelBuilder.Entity(\&quot;Simulator.Server.Models.User\&quot;, b =&gt;\n    21\t                {\n    22\t                    b.Property&lt;int&gt;(\&quot;Id\&quot;)\n    23\t                        .ValueGeneratedOnAdd()\n    24\t                        .HasColumnType(\&quot;INTEGER\&quot;);\n    25\t\n    26\t                    b.Property&lt;DateTime&gt;(\&quot;CreatedAt\&quot;)\n    27\t                        .ValueGeneratedOnAdd()\n    28\t                        .HasColumnType(\&quot;TEXT\&quot;)\n    29\t                        .HasDefaultValueSql(\&quot;datetime('now')\&quot;);\n    30\t\n    31\t                    b.Property&lt;string&gt;(\&quot;Email\&quot;)\n    32\t                        .IsRequired()\n    33\t                        .HasMaxLength(255)\n    34\t                        .HasColumnType(\&quot;TEXT\&quot;);\n    35\t\n    36\t                    b.Property&lt;string&gt;(\&quot;Name\&quot;)\n    37\t                        .IsRequired()\n    38\t                        .HasMaxLength(100)\n    39\t                        .HasColumnType(\&quot;TEXT\&quot;);\n    40\t\n    41\t                    b.Property&lt;DateTime?&gt;(\&quot;UpdatedAt\&quot;)\n    42\t                        .HasColumnType(\&quot;TEXT\&quot;);\n    43\t\n    44\t                    b.HasKey(\&quot;Id\&quot;);\n    45\t\n    46\t                    b.HasIndex(\&quot;Email\&quot;)\n    47\t                        .IsUnique();\n    48\t\n    49\t                    b.ToTable(\&quot;Users\&quot;);\n    50\t                });\n    51\t#pragma warning restore 612, 618\n    52\t        }\n    53\t    }\n    54\t}\n...\nPath: src/Simulator.Server/Controllers/UsersController.cs\n     1\tusing Microsoft.AspNetCore.Mvc;\n     2\tusing FreeSql;\n     3\tusing Simulator.Server.Models;\n     4\t\n     5\tnamespace Simulator.Server.Controllers\n     6\t{\n     7\t    [ApiController]\n     8\t    [Route(\&quot;api/[controller]\&quot;)]\n     9\t    public class UsersController : ControllerBase\n    10\t    {\n    11\t        private readonly IFreeSql _freeSql;\n    12\t\n    13\t        public UsersController(IFreeSql freeSql)\n    14\t        {\n    15\t            _freeSql = freeSql;\n    16\t        }\n    17\t\n    18\t        // GET: api/users\n    19\t        [HttpGet]\n    20\t        public async Task&lt;ActionResult&lt;IEnumerable&lt;User&gt;&gt;&gt; GetUsers()\n    21\t        {\n    22\t            var users = await _freeSql.Select&lt;User&gt;().ToListAsync();\n    23\t            return Ok(users);\n    24\t        }\n    25\t\n    26\t        // GET: api/users/5\n    27\t        [HttpGet(\&quot;{id}\&quot;)]\n    28\t        public async Task&lt;ActionResult&lt;User&gt;&gt; GetUser(int id)\n    29\t        {\n    30\t            var user = await _freeSql.Select&lt;User&gt;().Where(u =&gt; u.Id == id).FirstAsync();\n    31\t\n    32\t            if (user == null)\n    33\t            {\n    34\t                return NotFound();\n    35\t            }\n    36\t\n    37\t            return user;\n    38\t        }\n    39\t\n    40\t        // POST: api/users\n    41\t        [HttpPost]\n    42\t        public async Task&lt;ActionResult&lt;User&gt;&gt; PostUser(User user)\n    43\t        {\n    44\t            user.CreatedAt = DateTime.Now;\n    45\t            var insertedUsers = await _freeSql.Insert(user).ExecuteInsertedAsync();\n    46\t            var insertedUser = insertedUsers.FirstOrDefault();\n    47\t\n    48\t            if (insertedUser == null)\n    49\t            {\n    50\t                return BadRequest(\&quot;Failed to create user\&quot;);\n    51\t            }\n    52\t\n    53\t            return CreatedAtAction(nameof(GetUser), new { id = insertedUser.Id }, insertedUser);\n    54\t        }\n    55\t\n    56\t        // PUT: api/users/5\n    57\t        [HttpPut(\&quot;{id}\&quot;)]\n    58\t        public async Task&lt;IActionResult&gt; PutUser(int id, User user)\n    59\t        {\n    60\t            if (id != user.Id)\n    61\t            {\n    62\t                return BadRequest();\n    63\t            }\n    64\t\n    65\t            user.UpdatedAt = DateTime.Now;\n    66\t            \n    67\t            var affectedRows = await _freeSql.Update&lt;User&gt;()\n    68\t                .SetSource(user)\n    69\t                .Where(u =&gt; u.Id == id)\n    70\t                .ExecuteAffrowsAsync();\n    71\t\n    72\t            if (affectedRows == 0)\n    73\t            {\n    74\t                return NotFound();\n    75\t            }\n    76\t\n    77\t            return NoContent();\n    78\t        }\n    79\t\n    80\t        // DELETE: api/users/5\n    81\t        [HttpDelete(\&quot;{id}\&quot;)]\n    82\t        public async Task&lt;IActionResult&gt; DeleteUser(int id)\n    83\t        {\n    84\t            var affectedRows = await _freeSql.Delete&lt;User&gt;()\n    85\t                .Where(u =&gt; u.Id == id)\n    86\t                .ExecuteAffrowsAsync();\n    87\t\n    88\t            if (affectedRows == 0)\n    89\t            {\n    90\t                return NotFound();\n    91\t            }\n    92\t\n    93\t            return NoContent();\n    94\t        }\n    95\t\n    96\t        private async Task&lt;bool&gt; UserExists(int id)\n    97\t        {\n    98\t            return await _freeSql.Select&lt;User&gt;().Where(u =&gt; u.Id == id).AnyAsync();\n    99\t        }\n   100\t    }\n   101\t}\n...\nPath: src/Simulator.Server/Migrations/20250707083248_InitialCreate.Designer.cs\n...\n    22\t\n    23\t            modelBuilder.Entity(\&quot;Simulator.Server.Models.User\&quot;, b =&gt;\n    24\t                {\n    25\t                    b.Property&lt;int&gt;(\&quot;Id\&quot;)\n    26\t                        .ValueGeneratedOnAdd()\n    27\t                        .HasColumnType(\&quot;INTEGER\&quot;);\n    28\t\n    29\t                    b.Property&lt;DateTime&gt;(\&quot;CreatedAt\&quot;)\n    30\t                        .ValueGeneratedOnAdd()\n    31\t                        .HasColumnType(\&quot;TEXT\&quot;)\n    32\t                        .HasDefaultValueSql(\&quot;datetime('now')\&quot;);\n    33\t\n    34\t                    b.Property&lt;string&gt;(\&quot;Email\&quot;)\n    35\t                        .IsRequired()\n    36\t                        .HasMaxLength(255)\n    37\t                        .HasColumnType(\&quot;TEXT\&quot;);\n    38\t\n    39\t                    b.Property&lt;string&gt;(\&quot;Name\&quot;)\n    40\t                        .IsRequired()\n    41\t                        .HasMaxLength(100)\n    42\t                        .HasColumnType(\&quot;TEXT\&quot;);\n    43\t\n    44\t                    b.Property&lt;DateTime?&gt;(\&quot;UpdatedAt\&quot;)\n    45\t                        .HasColumnType(\&quot;TEXT\&quot;);\n    46\t\n    47\t                    b.HasKey(\&quot;Id\&quot;);\n    48\t\n    49\t                    b.HasIndex(\&quot;Email\&quot;)\n    50\t                        .IsUnique();\n    51\t\n    52\t                    b.ToTable(\&quot;Users\&quot;);\n    53\t                });\n    54\t#pragma warning restore 612, 618\n    55\t        }\n    56\t    }\n    57\t}\n...\nPath: src/Simulator.Server/Data/ApplicationDbContext.cs\n     1\tusing Microsoft.EntityFrameworkCore;\n     2\tusing Simulator.Server.Models;\n     3\t\n     4\tnamespace Simulator.Server.Data\n     5\t{\n     6\t    public class ApplicationDbContext : DbContext\n     7\t    {\n     8\t        public ApplicationDbContext(DbContextOptions&lt;ApplicationDbContext&gt; options)\n     9\t            : base(options)\n    10\t        {\n    11\t        }\n    12\t\n    13\t        // DbSet 属性\n    14\t        public DbSet&lt;User&gt; Users { get; set; }\n    15\t\n    16\t        protected override void OnModelCreating(ModelBuilder modelBuilder)\n    17\t        {\n    18\t            base.OnModelCreating(modelBuilder);\n    19\t        }\n    20\t    }\n    21\t}\n...\nPath: src/Simulator.Server/Data/DatabaseExtensions.cs\n...\n    22\t\n    23\t        /// &lt;summary&gt;\n    24\t        /// 种子数据（可选）\n    25\t        /// &lt;/summary&gt;\n    26\t        /// &lt;param name=\&quot;app\&quot;&gt;&lt;/param&gt;\n    27\t        /// &lt;returns&gt;&lt;/returns&gt;\n    28\t        public static async Task&lt;IApplicationBuilder&gt; SeedDatabaseAsync(this IApplicationBuilder app)\n    29\t        {\n    30\t            using var scope = app.ApplicationServices.CreateScope();\n    31\t            var context = scope.ServiceProvider.GetRequiredService&lt;ApplicationDbContext&gt;();\n    32\t            \n    33\t            // 检查是否已有数据\n    34\t            if (!await context.Users.AnyAsync())\n    35\t            {\n    36\t                // 添加示例数据\n    37\t                var sampleUsers = new[]\n    38\t                {\n    39\t                    new Models.User { Name = \&quot;张三\&quot;, Email = \&quot;<EMAIL>\&quot; },\n    40\t                    new Models.User { Name = \&quot;李四\&quot;, Email = \&quot;<EMAIL>\&quot; },\n    41\t                    new Models.User { Name = \&quot;王五\&quot;, Email = \&quot;<EMAIL>\&quot; }\n    42\t                };\n    43\t\n    44\t                context.Users.AddRange(sampleUsers);\n    45\t                await context.SaveChangesAsync();\n    46\t            }\n    47\t            \n    48\t            return app;\n    49\t        }\n    50\t    }\n    51\t}\n...\nPath: docs/README_EFCore.md\n...\n    30\t\n    31\t- **开发环境**: `Data Source=simulator_dev.db`\n    32\t- **生产环境**: `Data Source=simulator.db`\n    33\t\n    34\t### 数据库上下文\n    35\t\n    36\t`ApplicationDbContext` 类位于 `Data/ApplicationDbContext.cs`，包含：\n    37\t- 实体配置\n    38\t- 关系映射\n    39\t- 约束定义\n    40\t\n    41\t## 使用示例\n    42\t\n    43\t### 1. 添加新实体\n    44\t\n    45\t1. 在 `Models/` 目录下创建实体类\n    46\t2. 在 `ApplicationDbContext` 中添加 `DbSet&lt;T&gt;` 属性\n    47\t3. 在 `OnModelCreating` 方法中配置实体关系\n    48\t4. 创建迁移...&quot;,&quot;is_error&quot;:false,&quot;request_id&quot;:&quot;&quot;,&quot;tool_use_id&quot;:&quot;toolu_01NBbDSn4Dk6nnwyjq3ESvPc&quot;}},{&quot;id&quot;:2,&quot;type&quot;:4,&quot;ide_state_node&quot;:{&quot;workspaceFolders&quot;:[{&quot;repositoryRoot&quot;:&quot;C:/Users/<USER>/Projects/OcuSimulator&quot;,&quot;folderRoot&quot;:&quot;C:/Users/<USER>/Projects/OcuSimulator&quot;}],&quot;workspaceFoldersUnchanged&quot;:false,&quot;currentTerminal&quot;:{&quot;terminalId&quot;:19,&quot;currentWorkingDirectory&quot;:&quot;C:/Users/<USER>/Projects/OcuSimulator&quot;}}}],&quot;timestamp&quot;:&quot;2025-07-08T06:58:25.077Z&quot;,&quot;response_text&quot;:&quot;现在让我搜索更多关于FreeSql中ExecuteIdentity和Column Identity的具体信息：\n&quot;,&quot;workspace_file_chunks&quot;:[]},{&quot;status&quot;:&quot;success&quot;,&quot;request_id&quot;:&quot;6d936d91-a25b-410f-8c89-135df7e094c3&quot;,&quot;request_message&quot;:&quot;&quot;,&quot;structured_output_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:5,&quot;content&quot;:&quot;&quot;,&quot;tool_use&quot;:{&quot;tool_use_id&quot;:&quot;toolu_011mxat4LGz4UvpKT7D1T8tm&quot;,&quot;tool_name&quot;:&quot;web-fetch&quot;,&quot;input_json&quot;:&quot;{\&quot;url\&quot;: \&quot;https://github.com/dotnetcore/FreeSql/wiki/Insert-Data\&quot;}&quot;,&quot;mcp_server_name&quot;:&quot;&quot;,&quot;mcp_tool_name&quot;:&quot;&quot;}},{&quot;id&quot;:0,&quot;type&quot;:0,&quot;content&quot;:&quot;&quot;},{&quot;id&quot;:2,&quot;type&quot;:2,&quot;content&quot;:&quot;&quot;},{&quot;id&quot;:3,&quot;type&quot;:3,&quot;content&quot;:&quot;&quot;}],&quot;seen_state&quot;:&quot;seen&quot;,&quot;structured_request_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:1,&quot;tool_result_node&quot;:{&quot;content&quot;:&quot;- [Repository | FreeSql](https://freesql.net/en/guide/repository.html)\n  Jul 26, 2024 ... Insert data, optimized execution with ExecuteAffrows/ExecuteIdentity/ExecuteInserted ... //If the ServerTime property exists in the Column ...\n\n- [Insert Data · dotnetcore/FreeSql Wiki · GitHub](https://github.com/dotnetcore/FreeSql/wiki/Insert-Data)\n  Dec 14, 2023 ... If the table has auto-increment columns, id will be returned after inserting data. ... To execute BulkCopy to import data in batches, you need to ...\n\n- [Insert or Update | FreeSql](https://freesql.net/en/guide/insert-or-update.html)\n  Aug 21, 2021 ... ... attribute of the ... perform insert or update operations (batch supported). class TestInfo { [Column(IsIdentity = true)] public int id ...\n\n- [Repository Layer · dotnetcore/FreeSql Wiki · GitHub](https://github.com/dotnetcore/FreeSql/wiki/Repository-layer)\n  Jan 28, 2024 ... ... execution ExecuteAffrows , ExecuteIdentity or ExecuteInserted ; ... attributes, the batch insertion of the repository will be executed one by one.\n\n- [Getting Started · dotnetcore/FreeSql Wiki · GitHub](https://github.com/dotnetcore/FreeSql/wiki/Getting-Started)\n  Nov 22, 2023 ... Models. FreeSql uses a model to perform data access. The model is represented by an entity class to represent a database table or ...&quot;,&quot;is_error&quot;:false,&quot;request_id&quot;:&quot;&quot;,&quot;tool_use_id&quot;:&quot;toolu_01DN1FtLCx4nddUkhSnJzZFS&quot;}},{&quot;id&quot;:2,&quot;type&quot;:4,&quot;ide_state_node&quot;:{&quot;workspaceFolders&quot;:[{&quot;repositoryRoot&quot;:&quot;C:/Users/<USER>/Projects/OcuSimulator&quot;,&quot;folderRoot&quot;:&quot;C:/Users/<USER>/Projects/OcuSimulator&quot;}],&quot;workspaceFoldersUnchanged&quot;:false,&quot;currentTerminal&quot;:{&quot;terminalId&quot;:19,&quot;currentWorkingDirectory&quot;:&quot;C:/Users/<USER>/Projects/OcuSimulator&quot;}}}],&quot;timestamp&quot;:&quot;2025-07-08T06:58:33.187Z&quot;,&quot;response_text&quot;:&quot;&quot;,&quot;workspace_file_chunks&quot;:[]},{&quot;status&quot;:&quot;success&quot;,&quot;request_id&quot;:&quot;add94674-a247-47f6-9766-7b7afd237bd9&quot;,&quot;request_message&quot;:&quot;&quot;,&quot;structured_output_nodes&quot;:[{&quot;id&quot;:0,&quot;type&quot;:0,&quot;content&quot;:&quot;基于我对代码的分析和FreeSql文档的查阅，我来为您详细解释`ExecuteIdentity`和普通`Execute`的区别，以及`Column(Identity=True)`的作用：\n\n## ExecuteIdentity vs Execute 的区别\n\n### 1. **ExecuteIdentity**\n- **用途**：专门用于插入数据后返回**自增主键的值**\n- **返回值**：`long` 类型，返回数据库生成的自增ID\n- **使用场景**：当表有自增主键时，插入数据后需要获取生成的ID值\n\n&lt;augment_code_snippet path=\&quot;src/Simulator.Server/Controllers/UserController.cs\&quot; mode=\&quot;EXCERPT\&quot;&gt;\n```csharp\nsql.Insert(astudentbutmaybeateacher).ExecuteIdentity();\n```\n&lt;/augment_code_snippet&gt;\n\n### 2. **ExecuteAffrows** (常见的Execute方法)\n- **用途**：执行SQL并返回**受影响的行数**\n- **返回值**：`long` 类型，返回受影响的行数\n- **使用场景**：当你只需要知道操作是否成功，不需要获取自增ID时\n\n```csharp\n// 返回受影响的行数\nlong affectedRows = sql.Insert(user).ExecuteAffrows();\n```\n\n### 3. **ExecuteInserted**\n- **用途**：执行SQL并返回**插入后的完整记录**\n- **返回值**：`List&lt;T&gt;` 类型，包含插入后的完整实体（包括自增ID）\n- **使用场景**：需要获取插入后的完整记录信息\n\n## Column(Identity=True) 的作用\n\n`[Column(Identity=True)]` 是FreeSql中的特性标记，用于：\n\n### 1. **标识自增列**\n```csharp\nclass Topic {\n    [Column(IsIdentity = true, IsPrimary = true)]\n    public int Id { get; set; }\n    public string Title { get; set; }\n}\n```\n\n### 2. **主要作用**：\n- **告诉FreeSql这是一个自增主键列**\n- **插入时自动跳过该列**（数据库会自动生成值）\n- **使用ExecuteIdentity时能正确返回生成的ID值**\n- **批量插入时能正确回填自增ID到实体对象**\n\n### 3. **数据库行为**：\n- 插入时SQL不会包含该列\n- 数据库自动生成递增的数值\n- ExecuteIdentity能获取到数据库生成的值\n\n## 您当前代码的问题\n\n查看您的User模型：\n\n&lt;augment_code_snippet path=\&quot;src/Simulator.Server/Models/User.cs\&quot; mode=\&quot;EXCERPT\&quot;&gt;\n```csharp\npublic class User\n{\n    [Key] public required string Id { get; init; }\n    public required string Username { get; init; }\n    public required string Password { get; init; }\n    public required bool IsTeacher { get; init; }\n}\n```\n&lt;/augment_code_snippet&gt;\n\n**问题**：您的`Id`是`string`类型且手动赋值（使用Nanoid），但在Controller中使用了`ExecuteIdentity()`：\n\n&lt;augment_code_snippet path=\&quot;src/Simulator.Server/Controllers/UserController.cs\&quot; mode=\&quot;EXCERPT\&quot;&gt;\n```csharp\nvar astudentbutmaybeateacher = new User\n{\n    Id = Nanoid.Generate(), // 手动生成ID\n    Username = username,\n    Password = password,\n    IsTeacher = isTeacher\n};\n\nsql.Insert(astudentbutmaybeateacher).ExecuteIdentity(); // 这里不合适\n```\n&lt;/augment_code_snippet&gt;\n\n## 建议的修改\n\n由于您使用的是手动生成的字符串ID，应该使用`ExecuteAffrows()`而不是`ExecuteIdentity()`：\n\n```csharp\n// 推荐做法\nvar result = sql.Insert(astudentbutmaybeateacher).ExecuteAffrows();\nif (result &gt; 0)\n{\n    return Ok(astudentbutmaybeateacher);\n}\nelse\n{\n    return BadRequest(\&quot;插入失败\&quot;);\n}\n```\n\n或者如果您想改为使用自增整数ID：\n\n```csharp\npublic class User\n{\n    [Column(IsIdentity = true, IsPrimary = true)]\n    public int Id { get; set; } // 改为int自增\n    public required string Username { get; init; }\n    public required string Password { get; init; }\n    public required bool IsTeacher { get; init; }\n}\n\n// Controller中\nvar user = new User\n{\n    // 不设置Id，让数据库自动生成\n    Username = username,\n    Password = password,\n    IsTeacher = isTeacher\n};\n\nlong newId = sql.Insert(user).ExecuteIdentity();\nuser.Id = (int)newId; // 回填ID\n```\n\n总结：`ExecuteIdentity`专门用于获取数据库自增主键，而您当前使用手动生成的字符串ID，所以应该使用`ExecuteAffrows()`。&quot;},{&quot;id&quot;:2,&quot;type&quot;:2,&quot;content&quot;:&quot;基于我对代码的分析和FreeSql文档的查阅，我来为您详细解释`ExecuteIdentity`和普通`Execute`的区别，以及`Column(Identity=True)`的作用：\n\n## ExecuteIdentity vs Execute 的区别\n\n### 1. **ExecuteIdentity**\n- **用途**：专门用于插入数据后返回**自增主键的值**\n- **返回值**：`long` 类型，返回数据库生成的自增ID\n- **使用场景**：当表有自增主键时，插入数据后需要获取生成的ID值\n\n````csharp path=src/Simulator.Server/Controllers/UserController.cs mode=EXCERPT\nsql.Insert(astudentbutmaybeateacher).ExecuteIdentity();\n````\n\n### 2. **ExecuteAffrows** (常见的Execute方法)\n- **用途**：执行SQL并返回**受影响的行数**\n- **返回值**：`long` 类型，返回受影响的行数\n- **使用场景**：当你只需要知道操作是否成功，不需要获取自增ID时\n\n```csharp\n// 返回受影响的行数\nlong affectedRows = sql.Insert(user).ExecuteAffrows();\n```\n\n### 3. **ExecuteInserted**\n- **用途**：执行SQL并返回**插入后的完整记录**\n- **返回值**：`List&lt;T&gt;` 类型，包含插入后的完整实体（包括自增ID）\n- **使用场景**：需要获取插入后的完整记录信息\n\n## Column(Identity=True) 的作用\n\n`[Column(Identity=True)]` 是FreeSql中的特性标记，用于：\n\n### 1. **标识自增列**\n```csharp\nclass Topic {\n    [Column(IsIdentity = true, IsPrimary = true)]\n    public int Id { get; set; }\n    public string Title { get; set; }\n}\n```\n\n### 2. **主要作用**：\n- **告诉FreeSql这是一个自增主键列**\n- **插入时自动跳过该列**（数据库会自动生成值）\n- **使用ExecuteIdentity时能正确返回生成的ID值**\n- **批量插入时能正确回填自增ID到实体对象**\n\n### 3. **数据库行为**：\n- 插入时SQL不会包含该列\n- 数据库自动生成递增的数值\n- ExecuteIdentity能获取到数据库生成的值\n\n## 您当前代码的问题\n\n查看您的User模型：\n\n````csharp path=src/Simulator.Server/Models/User.cs mode=EXCERPT\npublic class User\n{\n    [Key] public required string Id { get; init; }\n    public required string Username { get; init; }\n    public required string Password { get; init; }\n    public required bool IsTeacher { get; init; }\n}\n````\n\n**问题**：您的`Id`是`string`类型且手动赋值（使用Nanoid），但在Controller中使用了`ExecuteIdentity()`：\n\n````csharp path=src/Simulator.Server/Controllers/UserController.cs mode=EXCERPT\nvar astudentbutmaybeateacher = new User\n{\n    Id = Nanoid.Generate(), // 手动生成ID\n    Username = username,\n    Password = password,\n    IsTeacher = isTeacher\n};\n\nsql.Insert(astudentbutmaybeateacher).ExecuteIdentity(); // 这里不合适\n````\n\n## 建议的修改\n\n由于您使用的是手动生成的字符串ID，应该使用`ExecuteAffrows()`而不是`ExecuteIdentity()`：\n\n```csharp\n// 推荐做法\nvar result = sql.Insert(astudentbutmaybeateacher).ExecuteAffrows();\nif (result &gt; 0)\n{\n    return Ok(astudentbutmaybeateacher);\n}\nelse\n{\n    return BadRequest(\&quot;插入失败\&quot;);\n}\n```\n\n或者如果您想改为使用自增整数ID：\n\n```csharp\npublic class User\n{\n    [Column(IsIdentity = true, IsPrimary = true)]\n    public int Id { get; set; } // 改为int自增\n    public required string Username { get; init; }\n    public required string Password { get; init; }\n    public required bool IsTeacher { get; init; }\n}\n\n// Controller中\nvar user = new User\n{\n    // 不设置Id，让数据库自动生成\n    Username = username,\n    Password = password,\n    IsTeacher = isTeacher\n};\n\nlong newId = sql.Insert(user).ExecuteIdentity();\nuser.Id = (int)newId; // 回填ID\n```\n\n总结：`ExecuteIdentity`专门用于获取数据库自增主键，而您当前使用手动生成的字符串ID，所以应该使用`ExecuteAffrows()`。\n&quot;},{&quot;id&quot;:3,&quot;type&quot;:3,&quot;content&quot;:&quot;&quot;}],&quot;seen_state&quot;:&quot;unseen&quot;,&quot;structured_request_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:1,&quot;tool_result_node&quot;:{&quot;content&quot;:&quot;                 Insert Data · dotnetcore/FreeSql Wiki · GitHub                                          \n\n[Skip to content](#start-of-content)   \n\nNavigation Menu\n---------------\n\nToggle navigation\n\n[](/)\n\n[Sign in](/login?return_to=https%3A%2F%2Fgithub.com%2Fdotnetcore%2FFreeSql%2Fwiki%2FInsert-Data)\n\nAppearance settings\n\n*   Product\n    \n    *   [\n        \n        GitHub Copilot\n        \n        Write better code with AI\n        \n        ](https://github.com/features/copilot)\n    *   [\n        \n        GitHub Models New\n        \n        Manage and compare prompts\n        \n        ](https://github.com/features/models)\n    *   [\n        \n        GitHub Advanced Security\n        \n        Find and fix vulnerabilities\n        \n        ](https://github.com/security/advanced-security)\n    *   [\n        \n        Actions\n        \n        Automate any workflow\n        \n        ](https://github.com/features/actions)\n    *   [\n        \n        Codespaces\n        \n        Instant dev environments\n        \n        ](https://github.com/features/codespaces)\n    \n    *   [\n        \n        Issues\n        \n        Plan and track work\n        \n        ](https://github.com/features/issues)\n    *   [\n        \n        Code Review\n        \n        Manage code changes\n        \n        ](https://github.com/features/code-review)\n    *   [\n        \n        Discussions\n        \n        Collaborate outside of code\n        \n        ](https://github.com/features/discussions)\n    *   [\n        \n        Code Search\n        \n        Find more, search less\n        \n        ](https://github.com/features/code-search)\n    \n    Explore\n    \n    *   [Why GitHub](https://github.com/why-github)\n    *   [All features](https://github.com/features)\n    *   [Documentation](https://docs.github.com)\n    *   [GitHub Skills](https://skills.github.com)\n    *   [Blog](https://github.blog)\n    \n*   Solutions\n    \n    By company size\n    \n    *   [Enterprises](https://github.com/enterprise)\n    *   [Small and medium teams](https://github.com/team)\n    *   [Startups](https://github.com/enterprise/startups)\n    *   [Nonprofits](/solutions/industry/nonprofits)\n    \n    By use case\n    \n    *   [DevSecOps](/solutions/use-case/devsecops)\n    *   [DevOps](/solutions/use-case/devops)\n    *   [CI/CD](/solutions/use-case/ci-cd)\n    *   [View all use cases](/solutions/use-case)\n    \n    By industry\n    \n    *   [Healthcare](/solutions/industry/healthcare)\n    *   [Financial services](/solutions/industry/financial-services)\n    *   [Manufacturing](/solutions/industry/manufacturing)\n    *   [Government](/solutions/industry/government)\n    *   [View all industries](/solutions/industry)\n    \n    [View all solutions](/solutions)\n    \n*   Resources\n    \n    Topics\n    \n    *   [AI](/resources/articles/ai)\n    *   [DevOps](/resources/articles/devops)\n    *   [Security](/resources/articles/security)\n    *   [Software Development](/resources/articles/software-development)\n    *   [View all](/resources/articles)\n    \n    Explore\n    \n    *   [Learning Pathways](https://resources.github.com/learn/pathways)\n    *   [Events &amp; Webinars](https://resources.github.com)\n    *   [Ebooks &amp; Whitepapers](https://github.com/resources/whitepapers)\n    *   [Customer Stories](https://github.com/customer-stories)\n    *   [Partners](https://partner.github.com)\n    *   [Executive Insights](https://github.com/solutions/executive-insights)\n    \n*   Open Source\n    \n    *   [\n        \n        GitHub Sponsors\n        \n        Fund open source developers\n        \n        ](/sponsors)\n    \n    *   [\n        \n        The ReadME Project\n        \n        GitHub community articles\n        \n        ](https://github.com/readme)\n    \n    Repositories\n    \n    *   [Topics](https://github.com/topics)\n    *   [Trending](https://github.com/trending)\n    *   [Collections](https://github.com/collections)\n    \n*   Enterprise\n    \n    *   [\n        \n        Enterprise platform\n        \n        AI-powered developer platform\n        \n        ](/enterprise)\n    \n    Available add-ons\n    \n    *   [\n        \n        GitHub Advanced Security\n        \n        Enterprise-grade security features\n        \n        ](https://github.com/security/advanced-security)\n    *   [\n        \n        Copilot for business\n        \n        Enterprise-grade AI features\n        \n        ](/features/copilot/copilot-business)\n    *   [\n        \n        Premium Support\n        \n        Enterprise-grade 24/7 support\n        \n        ](/premium-support)\n    \n*   [Pricing](https://github.com/pricing)\n\nSearch or jump to...\n\nSearch code, repositories, users, issues, pull requests...\n==========================================================\n\nSearch\n\nClear\n\n[Search syntax tips](https://docs.github.com/search-github/github-code-search/understanding-github-code-search-syntax)\n\nProvide feedback\n================\n\nWe read every piece of feedback, and take your input very seriously.\n\n Include my email address so I can be contacted\n\nCancel Submit feedback\n\nSaved searches\n==============\n\nUse saved searches to filter your results more quickly\n------------------------------------------------------\n\nName  \n\nQuery \n\nTo see all available qualifiers, see our [documentation](https://docs.github.com/search-github/github-code-search/understanding-github-code-search-syntax).\n\nCancel Create saved search\n\n[Sign in](/login?return_to=https%3A%2F%2Fgithub.com%2Fdotnetcore%2FFreeSql%2Fwiki%2FInsert-Data)\n\n[Sign up](/signup?ref_cta=Sign+up&amp;ref_loc=header+logged+out&amp;ref_page=%2F%3Cuser-name%3E%2F%3Crepo-name%3E%2Fwiki%2Fshow&amp;source=header-repo&amp;source_repo=dotnetcore%2FFreeSql)\n\nAppearance settings\n\nResetting focus\n\nYou signed in with another tab or window. Reload to refresh your session. You signed out in another tab or window. Reload to refresh your session. You switched accounts on another tab or window. Reload to refresh your session. Dismiss alert\n\n[dotnetcore](/dotnetcore) / **[FreeSql](/dotnetcore/FreeSql)** Public\n\n*   [Notifications](/login?return_to=%2Fdotnetcore%2FFreeSql) You must be signed in to change notification settings\n*   [Fork 885](/login?return_to=%2Fdotnetcore%2FFreeSql)\n*   [Star 4.3k](/login?return_to=%2Fdotnetcore%2FFreeSql)\n    \n\n*   [Code](/dotnetcore/FreeSql)\n*   [Issues 72](/dotnetcore/FreeSql/issues)\n*   [Pull requests 7](/dotnetcore/FreeSql/pulls)\n*   [Discussions](/dotnetcore/FreeSql/discussions)\n*   [Actions](/dotnetcore/FreeSql/actions)\n*   [Projects 0](/dotnetcore/FreeSql/projects)\n*   [Models](/dotnetcore/FreeSql/models)\n*   [Wiki](/dotnetcore/FreeSql/wiki)\n*   [Security](/dotnetcore/FreeSql/security)\n    \n    [](/dotnetcore/FreeSql/security)\n    \n    [](/dotnetcore/FreeSql/security)\n    \n    [](/dotnetcore/FreeSql/security)\n    \n    [\n    \n    ### Uh oh!\n    \n    ](/dotnetcore/FreeSql/security)\n    \n    [There was an error while loading.](/dotnetcore/FreeSql/security) Please reload this page.\n    \n*   [Insights](/dotnetcore/FreeSql/pulse)\n\nAdditional navigation options\n\n*   [Code](/dotnetcore/FreeSql)\n*   [Issues](/dotnetcore/FreeSql/issues)\n*   [Pull requests](/dotnetcore/FreeSql/pulls)\n*   [Discussions](/dotnetcore/FreeSql/discussions)\n*   [Actions](/dotnetcore/FreeSql/actions)\n*   [Projects](/dotnetcore/FreeSql/projects)\n*   [Models](/dotnetcore/FreeSql/models)\n*   [Wiki](/dotnetcore/FreeSql/wiki)\n*   [Security](/dotnetcore/FreeSql/security)\n*   [Insights](/dotnetcore/FreeSql/pulse)\n\nInsert Data\n===========\n\n[Jump to bottom](#wiki-pages-box)\n\n2881099 edited this page Dec 14, 2023 · [15 revisions](/dotnetcore/FreeSql/wiki/Insert-Data/_history)\n\n[中文](%e6%b7%bb%e5%8a%a0) | **English**\n\nFreeSql provides methods for inserting data in single and batches, and it can also return the inserted records when executed in a specific database.\n\nstatic IFreeSql fsql \\= new FreeSql.FreeSqlBuilder()\n    .UseConnectionString(FreeSql.DataType.MySql, connectionString)\n    .UseAutoSyncStructure(true) //Automatically synchronize the entity structure to the database\n    .Build(); //Be sure to define as singleton mode\n\nclass Topic {\n    \\[Column(IsIdentity \\= true, IsPrimary \\= true)\\]\n    public int Id { get; set; }\n    public int Clicks { get; set; }\n    public string Title { get; set; }\n    public DateTime CreateTime { get; set; }\n}\n\nvar items \\= new List&lt;Topic\\&gt;();\nfor (var a \\= 0; a &lt; 10; a++) items.Add(new Topic { Title \\= $\&quot;newtitle{a}\&quot;, Clicks \\= a \\* 100 });\n\n1\\. Single Insert\n-----------------\n\n[](#1-single-insert)\n\nvar t1 \\= fsql.Insert(items\\[0\\]).ExecuteAffrows();\n//INSERT INTO \\`Topic\\`(\\`Clicks\\`, \\`Title\\`, \\`CreateTime\\`) \n//VALUES(@Clicks0, @Title0, @CreateTime0)\n\nIf the table has auto-increment columns, `id` will be returned after inserting data.\n\nMethod 1: (Original)\n\nlong id \\= fsql.Insert(items\\[0\\]).ExecuteIdentity();\nitems\\[0\\].Id \\= id;\n\nMethod 2: (depends on FreeSql.Repository)\n\nvar repo \\= fsql.GetRepository&lt;Topic\\&gt;();\nrepo.Insert(items\\[0\\]);\n\n&gt; In the internal implementation, after inserting the data, the self-incremental value will be assigned to `items[0].Id` (support batch insert backfill)\n\n&gt; DbFirst sequence: \\[Column(IsIdentity = true, InsertValueSql = \&quot;seqname.nextval\&quot;)\\]\n\n2\\. Batch Insert\n----------------\n\n[](#2-batch-insert)\n\nvar t2 \\= fsql.Insert(items).ExecuteAffrows();\n//INSERT INTO \\`Topic\\`(\\`Clicks\\`, \\`Title\\`, \\`CreateTime\\`) \n//VALUES(@Clicks0, @Title0, @CreateTime0), (@Clicks1, @Title1, @CreateTime1), \n//(@Clicks2, @Title2, @CreateTime2), (@Clicks3, @Title3, @CreateTime3), \n//(@Clicks4, @Title4, @CreateTime4), (@Clicks5, @Title5, @CreateTime5), \n//(@Clicks6, @Title6, @CreateTime6), (@Clicks7, @Title7, @CreateTime7), \n//(@Clicks8, @Title8, @CreateTime8), (@Clicks9, @Title9, @CreateTime9)\n\n&gt; The errors that are easily caused by adding SqlServer in batches have been resolved:\n&gt; \n&gt;     The incoming request has too many parameters. The server supports a maximum of 2100 parameters. Reduce the number of parameters and resend the request.\n&gt;     \n&gt; \n&gt; Principle: Split into multiple packages and execute them in transactions.\n\nWhen inserting large quantities of data, the internal logic is divided and executed in batches. The segmentation rules are as follows:\n\nQuantity\n\nSize of Parameters\n\nMySql\n\n5000\n\n3000\n\nPostgreSQL\n\n5000\n\n3000\n\nSqlServer\n\n1000\n\n2100\n\nOracle\n\n500\n\n999\n\nSqlite\n\n5000\n\n999\n\n&gt; Quantity: It is the size of each batch of division. For example, a batch of 10,000 pieces of data will be inserted into two batches when mysql is executed.  \n&gt; Size of Parameters: the size of the parameter size divided into each batch. For example, when inserting 10,000 pieces of data in batches, each row needs to use 5 parameterizations, which will be divided into 3000/5 for each batch when mysql is executed.\n\nAfter the execution of the split, when the external transaction is not provided, the internal transaction is opened to achieve insertion integrity. You can also set appropriate values through `BatchOptions`.\n\nFreeSql adapts to the use of parameterization and non-parameterization of each data type. It is recommended to turn off the parameterization function for batch insertion and use `.NoneParameter()` to execute it.\n\n3\\. BulkCopy\n------------\n\n[](#3-bulkcopy)\n\npackage name\n\nmethod\n\ndesc\n\nFreeSql.Provider.SqlServer\n\nExecuteSqlBulkCopy\n\nFreeSql.Provider.MySqlConnector\n\nExecuteMySqlBulkCopy\n\nFreeSql.Provider.Oracle\n\nExecuteOracleBulkCopy\n\nFreeSql.Provider.Dameng\n\nExecuteDmBulkCopy\n\n达梦\n\nFreeSql.Provider.PostgreSQL\n\nExecutePgCopy\n\nFreeSql.Provider.KingbaseES\n\nExecuteKdbCopy\n\n人大金仓\n\nbulk insert test reference (52 fields)\n\n18W\n\n1W\n\n5K\n\n2K\n\n1K\n\n500\n\n100\n\n50\n\nMySql 5.5 ExecuteAffrows\n\n38,481\n\n2,234\n\n1,136\n\n284\n\n239\n\n167\n\n66\n\n30\n\nMySql 5.5 ExecuteMySqlBulkCopy\n\n28,405\n\n1,142\n\n657\n\n451\n\n435\n\n592\n\n47\n\n22\n\nSqlServer Express ExecuteAffrows\n\n402,355\n\n24,847\n\n11,465\n\n4,971\n\n2,437\n\n915\n\n138\n\n88\n\nSqlServer Express ExecuteSqlBulkCopy\n\n21,065\n\n578\n\n326\n\n139\n\n105\n\n79\n\n60\n\n48\n\nPostgreSQL 10 ExecuteAffrows\n\n46,756\n\n3,294\n\n2,269\n\n1,019\n\n374\n\n209\n\n51\n\n37\n\nPostgreSQL 10 ExecutePgCopy\n\n10,090\n\n583\n\n337\n\n136\n\n88\n\n61\n\n30\n\n25\n\n&gt; Explanation of 8W: insert 180000 rows of records, and the number in the table is the execution time (unit: ms)\n\nbulk insert test reference (10 fields)\n\n18W\n\n1W\n\n5K\n\n2K\n\n1K\n\n500\n\n100\n\n50\n\nMySql 5.5 ExecuteAffrows\n\n11,171\n\n866\n\n366\n\n80\n\n83\n\n50\n\n24\n\n34\n\nMySql 5.5 ExecuteMySqlBulkCopy\n\n6,504\n\n399\n\n257\n\n116\n\n87\n\n100\n\n16\n\n16\n\nSqlServer Express ExecuteAffrows\n\n47,204\n\n2,275\n\n1,108\n\n488\n\n279\n\n123\n\n35\n\n16\n\nSqlServer Express ExecuteSqlBulkCopy\n\n4,248\n\n127\n\n71\n\n30\n\n48\n\n14\n\n11\n\n10\n\nPostgreSQL 10 ExecuteAffrows\n\n9,786\n\n568\n\n336\n\n157\n\n102\n\n34\n\n9\n\n6\n\nPostgreSQL 10 ExecutePgCopy\n\n4,081\n\n167\n\n93\n\n39\n\n21\n\n12\n\n4\n\n2\n\n&gt; The test results are all based on the same operating system, and all are preheated.\n\n4\\. Dynamic tablename\n---------------------\n\n[](#4-dynamic-tablename)\n\nfsql.Insert(items).AsTable(\&quot;Topic\\_201903\&quot;).ExecuteAffrows();\n\n5\\. Insert the specified columns\n--------------------------------\n\n[](#5-insert-the-specified-columns)\n\nvar t3 \\= fsql.Insert(items).InsertColumns(a \\=&gt; a.Title).ExecuteAffrows();\n//INSERT INTO \\`Topic\\`(\\`Title\\`) \n//VALUES(@Title0), (@Title1), (@Title2), (@Title3), (@Title4), \n//(@Title5), (@Title6), (@Title7), (@Title8), (@Title9)\n\nvar t4 \\= fsql.Insert(items).InsertColumns(a \\=&gt;new { a.Title, a.Clicks }).ExecuteAffrows();\n//INSERT INTO \\`Topic\\`(\\`Clicks\\`, \\`Title\\`) \n//VALUES(@Clicks0, @Title0), (@Clicks1, @Title1), (@Clicks2, @Title2), \n//(@Clicks3, @Title3), (@Clicks4, @Title4), (@Clicks5, @Title5), \n//(@Clicks6, @Title6), (@Clicks7, @Title7), (@Clicks8, @Title8), \n//(@Clicks9, @Title9)\n\n6\\. Ignore the specified columns\n--------------------------------\n\n[](#6-ignore-the-specified-columns)\n\nvar t5 \\= fsql.Insert(items).IgnoreColumns(a \\=&gt; a.CreateTime).ExecuteAffrows();\n//INSERT INTO \\`Topic\\`(\\`Clicks\\`, \\`Title\\`) \n//VALUES(@Clicks0, @Title0), (@Clicks1, @Title1), (@Clicks2, @Title2), \n//(@Clicks3, @Title3), (@Clicks4, @Title4), (@Clicks5, @Title5), \n//(@Clicks6, @Title6), (@Clicks7, @Title7), (@Clicks8, @Title8), \n//(@Clicks9, @Title9)\n\nvar t6 \\= fsql.Insert(items).IgnoreColumns(a \\=&gt; new { a.Title, a.CreateTime }).ExecuteAffrows();\n///INSERT INTO \\`Topic\\`(\\`Clicks\\`) \n//VALUES(@Clicks0), (@Clicks1), (@Clicks2), (@Clicks3), (@Clicks4), \n//(@Clicks5), (@Clicks6), (@Clicks7), (@Clicks8), (@Clicks9)\n\n7\\. Column Insertion Priority\n-----------------------------\n\n[](#7-column-insertion-priority)\n\nAll Columns &lt; Specified columns (InsertColumns) &lt; Ignored Columns (IgnoreColumns)\n\n*   Without using `InsertColumns/IgnoreColumns`, all columns of the entity will be inserted into the database;\n*   Otherwise, when using `InsertColumns` and not using `IgnoreColumns`, only the specified columns are inserted into the database;\n*   Otherwise, in the case of using `IgnoreColumns`, only unspecified columns are inserted into the database.\n\n8、Dictionary Insert\n-------------------\n\n[](#8dictionary-insert)\n\nvar dic \\= new Dictionary&lt;string, object\\&gt;();\ndic.Add(\&quot;id\&quot;, 1);\ndic.Add(\&quot;name\&quot;, \&quot;xxxx\&quot;);\n\nfsql.InsertDict(dic).AsTable(\&quot;table1\&quot;).ExecuteAffrows();\n\n9\\. Import table data\n---------------------\n\n[](#9-import-table-data)\n\nint affrows \\= fsql.Select&lt;Topic\\&gt;()\n  .Limit(10)\n  .InsertInto(null, a \\=&gt; new Topic2\n  {\n    Title \\= a.Title\n  });\n\nINSERT INTO \\`Topic2\\`(\\`Title\\`, \\`Clicks\\`, \\`CreateTime\\`)\nSELECT a.\\`Title\\`, 0, '0001-01-01 00:00:00' \nFROM \\`Topic\\` a \nlimit 10\n\nNote: Because `Clicks` and `CreateTime` are not selected, it'll use the value set by the target entity attribute `[Column(InsertValueSql = xx)]`, or the default value of `C#` of the target entity attribute.\n\n10\\. `Insert Ignore Into` (MySql only)\n--------------------------------------\n\n[](#10-insert-ignore-into-mysql-only)\n\nfsql.Insert&lt;Topic\\&gt;().MySqlIgnoreInto().AppendData(items).ExecuteAffrows();\n///INSERT IGNORE INTO \\`Topic\\`(\\`Clicks\\`) \n//VALUES(@Clicks0), (@Clicks1), (@Clicks2), (@Clicks3), (@Clicks4), \n//(@Clicks5), (@Clicks6), (@Clicks7), (@Clicks8), (@Clicks9)\n\n11\\. `On Duplicate Key Update` (MySql only)\n-------------------------------------------\n\n[](#11-on-duplicate-key-update-mysql-only)\n\n[More information...](Insert-or-Update#6-on-duplicate-key-update-mysql-only)\n\n12\\. `On Conflict Do Update` (PostgreSQL only)\n----------------------------------------------\n\n[](#12-on-conflict-do-update-postgresql-only)\n\n[More information...](Insert-or-Update#7-on-conflict-do-update-postgresql-only)\n\nReference\n---------\n\n[](#reference)\n\n*   [《Database Transaction》](%e4%ba%8b%e5%8a%a1)\n*   [《FreeSql 101, Part 2: Delete Data》](Delete-Data)\n*   [《FreeSql 101, Part 3: Update Data》](Update-Data)\n*   [《FreeSql 101, Part 4: Query Data》](Query-Data)\n*   [《Repository Layer》](Repository-Layer)\n*   [《Filters and Global Filters》](Filters-and-Global-Filters)\n*   [《Sharding Tables and Database》](%e5%88%86%e8%a1%a8%e5%88%86%e5%ba%93)\n*   [《Tenant》](%e7%a7%9f%e6%88%b7)\n\nAPI\n---\n\n[](#api)\n\nMethods\n\nReturn\n\nParameters\n\nDescription\n\nAppendData\n\n&lt;this&gt;\n\nT1 | IEnumerable&lt;T1&gt;\n\nAppend the entity to be inserted\n\nInsertIdentity\n\n&lt;this&gt;\n\nNone\n\nSpecify the insert auto-increment column\n\nInsertColumns\n\n&lt;this&gt;\n\nLambda\n\nSpecify the inserted columns\n\nIgnoreColumns\n\n&lt;this&gt;\n\nLambda\n\nSpecify the ignored columns\n\nIgnoreInsertValueSql\n\n&lt;this&gt;\n\nLambda\n\nSpecify the ignored InsertValueSql columns\n\nCommandTimeout\n\n&lt;this&gt;\n\nint\n\nCommand timeout setting (seconds)\n\nWithTransaction\n\n&lt;this&gt;\n\nDbTransaction\n\nSet transaction object\n\nWithConnection\n\n&lt;this&gt;\n\nDbConnection\n\nSet the connection object\n\nToSql\n\nstring\n\nReturn the SQL statement to be executed.\n\n[OnDuplicateKeyUpdate](%e6%b7%bb%e5%8a%a0%e6%88%96%e4%bf%ae%e6%94%b9)\n\nOnDuplicateKeyUpdate&lt;T1&gt;\n\nNone\n\nMySql only. `On Duplicate Key Update`\n\n[OnConflictDoUpdate](%e6%b7%bb%e5%8a%a0%e6%88%96%e4%bf%ae%e6%94%b9)\n\nOnConflictDoUpdate&lt;T1&gt;\n\nNone\n\nPostgreSQL only. `On Conflict Do Update`\n\nExecuteAffrows\n\nlong\n\nExecute SQL and return the number of rows affected.\n\nExecuteIdentity\n\nlong\n\nExecute SQL and return the incremented value.\n\nExecuteInserted\n\nList&lt;T1&gt;\n\nExecute SQL and return the inserted records.\n\nExecuteSqlBulkCopy\n\nvoid\n\nSqlServer only. To execute BulkCopy to import data in batches, you need to reference `FreeSql.Provider.SqlServer`\n\nExecutePgCopy\n\nvoid\n\nPostgreSQL only. To execute BulkCopy to import data in batches, you need to reference `FreeSql.Provider.PostgreSQL`\n\nExecuteMySqlBulkCopy\n\nvoid\n\nMySql only. To execute BulkCopy to import data in batches, you need to reference `FreeSql.Provider.MysqlConnector`\n\nExecuteOracleBulkCopy\n\nvoid\n\nOracle only. To execute BulkCopy to import data in batches, you need to reference `FreeSql.Provider.Oracle`\n\nExecuteDmBulkCopy\n\nvoid\n\nDameng database only. To execute BulkCopy to import data in batches, you need to reference `FreeSql.Provider.Dameng`\n\n### Toggle table of contents Pages 79\n\n*   Loading\n    \n    [Home](/dotnetcore/FreeSql/wiki)\n    \n    ### Uh oh!\n    \n    There was an error while loading. Please reload this page.\n    \n*   Loading\n    \n    [ADO](/dotnetcore/FreeSql/wiki/ADO)\n    \n    ### Uh oh!\n    \n    There was an error while loading. Please reload this page.\n    \n*   Loading\n    \n    [AOP](/dotnetcore/FreeSql/wiki/AOP)\n    \n    ### Uh oh!\n    \n    There was an error while loading. Please reload this page.\n    \n*   Loading\n    \n    [API](/dotnetcore/FreeSql/wiki/API)\n    \n    ### Uh oh!\n    \n    There was an error while loading. Please reload this page.\n    \n*   Loading\n    \n    [BaseEntity](/dotnetcore/FreeSql/wiki/BaseEntity)\n    \n    ### Uh oh!\n    \n    There was an error while loading. Please reload this page.\n    \n*   Loading\n    \n    [Cascade Deletion](/dotnetcore/FreeSql/wiki/Cascade-Deletion)\n    \n    ### Uh oh!\n    \n    There was an error while loading. Please reload this page.\n    \n*   Loading\n    \n    [Cascade Saving](/dotnetcore/FreeSql/wiki/Cascade-Saving)\n    \n    ### Uh oh!\n    \n    There was an error while loading. Please reload this page.\n    \n*   Loading\n    \n    [CodeFirst](/dotnetcore/FreeSql/wiki/CodeFirst)\n    \n    ### Uh oh!\n    \n    There was an error while loading. Please reload this page.\n    \n*   Loading\n    \n    [Dapper比较](/dotnetcore/FreeSql/wiki/Dapper%E6%AF%94%E8%BE%83)\n    \n    ### Uh oh!\n    \n    There was an error while loading. Please reload this page.\n    \n*   Loading\n    \n    [DbContext](/dotnetcore/FreeSql/wiki/DbContext)\n    \n    ### Uh oh!\n    \n    There was an error while loading. Please reload this page.\n    \n*   Loading\n    \n    [DbFirst](/dotnetcore/FreeSql/wiki/DbFirst)\n    \n    ### Uh oh!\n    \n    There was an error while loading. Please reload this page.\n    \n*   Loading\n    \n    [Delete Data](/dotnetcore/FreeSql/wiki/Delete-Data)\n    \n    ### Uh oh!\n    \n    There was an error while loading. Please reload this page.\n    \n*   Loading\n    \n    [DI UnitOfWorkManager](/dotnetcore/FreeSql/wiki/DI-UnitOfWorkManager)\n    \n    ### Uh oh!\n    \n    There was an error while loading. Please reload this page.\n    \n*   Loading\n    \n    [Dynamic Operations](/dotnetcore/FreeSql/wiki/Dynamic-Operations)\n    \n    ### Uh oh!\n    \n    There was an error while loading. Please reload this page.\n    \n*   Loading\n    \n    [Entity Relationship](/dotnetcore/FreeSql/wiki/Entity-Relationship)\n    \n    ### Uh oh!\n    \n    There was an error while loading. Please reload this page.\n    \n*   Loading\n    \n    [EntityFramework比较](/dotnetcore/FreeSql/wiki/EntityFramework%E6%AF%94%E8%BE%83)\n    \n    ### Uh oh!\n    \n    There was an error while loading. Please reload this page.\n    \n*   Loading\n    \n    [FluentApi](/dotnetcore/FreeSql/wiki/FluentApi)\n    \n    ### Uh oh!\n    \n    There was an error while loading. Please reload this page.\n    \n*   Loading\n    \n    [Getting Started](/dotnetcore/FreeSql/wiki/Getting-Started)\n    \n    ### Uh oh!\n    \n    There was an error while loading. Please reload this page.\n    \n*   Loading\n    \n    [Greed Loading](/dotnetcore/FreeSql/wiki/Greed-Loading)\n    \n    ### Uh oh!\n    \n    There was an error while loading. Please reload this page.\n    \n*   Loading\n    \n    [Group Aggregation Query](/dotnetcore/FreeSql/wiki/Group-Aggregation-Query)\n    \n    ### Uh oh!\n    \n    There was an error while loading. Please reload this page.\n    \n*   Loading\n    \n    [Import Entity Configuration from Database](/dotnetcore/FreeSql/wiki/Import-Entity-Configuration-from-Database)\n    \n    ### Uh oh!\n    \n    There was an error while loading. Please reload this page.\n    \n*   Loading\n    \n    [Insert Data](/dotnetcore/FreeSql/wiki/Insert-Data)\n    \n    *   [1\\. Single Insert](/dotnetcore/FreeSql/wiki/Insert-Data#1-single-insert)\n    *   [2\\. Batch Insert](/dotnetcore/FreeSql/wiki/Insert-Data#2-batch-insert)\n    *   [3\\. BulkCopy](/dotnetcore/FreeSql/wiki/Insert-Data#3-bulkcopy)\n    *   [4\\. Dynamic tablename](/dotnetcore/FreeSql/wiki/Insert-Data#4-dynamic-tablename)\n    *   [5\\. Insert the specified columns](/dotnetcore/FreeSql/wiki/Insert-Data#5-insert-the-specified-columns)\n    *   [6\\. Ignore the specified columns](/dotnetcore/FreeSql/wiki/Insert-Data#6-ignore-the-specified-columns)\n    *   [7\\. Column Insertion Priority](/dotnetcore/FreeSql/wiki/Insert-Data#7-column-insertion-priority)\n    *   [8、Dictionary Insert](/dotnetcore/FreeSql/wiki/Insert-Data#8dictionary-insert)\n    *   [9\\. Import table data](/dotnetcore/FreeSql/wiki/Insert-Data#9-import-table-data)\n    *   [10\\. Insert Ignore Into (MySql only)](/dotnetcore/FreeSql/wiki/Insert-Data#10-insert-ignore-into-mysql-only)\n    *   [11\\. On Duplicate Key Update (MySql only)](/dotnetcore/FreeSql/wiki/Insert-Data#11-on-duplicate-key-update-mysql-only)\n    *   [12\\. On Conflict Do Update (PostgreSQL only)](/dotnetcore/FreeSql/wiki/Insert-Data#12-on-conflict-do-update-postgresql-only)\n    *   [Reference](/dotnetcore/FreeSql/wiki/Insert-Data#reference)\n    *   [API](/dotnetcore/FreeSql/wiki/Insert-Data#api)\n    \n*   Loading\n    \n    [Insert or Update](/dotnetcore/FreeSql/wiki/Insert-or-Update)\n    \n    ### Uh oh!\n    \n    There was an error while loading. Please reload this page.\n    \n*   Loading\n    \n    [Install](/dotnetcore/FreeSql/wiki/Install)\n    \n    ### Uh oh!\n    \n    There was an error while loading. Please reload this page.\n    \n*   Loading\n    \n    [Lazy Loading](/dotnetcore/FreeSql/wiki/Lazy-Loading)\n    \n    ### Uh oh!\n    \n    There was an error while loading. Please reload this page.\n    \n*   Loading\n    \n    [Linq to Sql](/dotnetcore/FreeSql/wiki/Linq-to-Sql)\n    \n    ### Uh oh!\n    \n    There was an error while loading. Please reload this page.\n    \n*   Loading\n    \n    [LinqToSql](/dotnetcore/FreeSql/wiki/LinqToSql)\n    \n    ### Uh oh!\n    \n    There was an error while loading. Please reload this page.\n    \n*   Loading\n    \n    [Nested Query](/dotnetcore/FreeSql/wiki/Nested-Query)\n    \n    ### Uh oh!\n    \n    There was an error while loading. Please reload this page.\n    \n*   Loading\n    \n    [Pagination](/dotnetcore/FreeSql/wiki/Pagination)\n    \n    ### Uh oh!\n    \n    There was an error while loading. Please reload this page.\n    \n*   Loading\n    \n    [Parent Child Relationship Query](/dotnetcore/FreeSql/wiki/Parent-Child-Relationship-Query)\n    \n    ### Uh oh!\n    \n    There was an error while loading. Please reload this page.\n    \n*   Loading\n    \n    [Query Data](/dotnetcore/FreeSql/wiki/Query-Data)\n    \n    ### Uh oh!\n    \n    There was an error while loading. Please reload this page.\n    \n*   Loading\n    \n    [Query from Multi Tables](/dotnetcore/FreeSql/wiki/Query-from-Multi-Tables)\n    \n    ### Uh oh!\n    \n    There was an error while loading. Please reload this page.\n    \n*   Loading\n    \n    [Query from Single Table](/dotnetcore/FreeSql/wiki/Query-from-Single-Table)\n    \n    ### Uh oh!\n    \n    There was an error while loading. Please reload this page.\n    \n*   Loading\n    \n    [Repository](/dotnetcore/FreeSql/wiki/Repository)\n    \n    ### Uh oh!\n    \n    There was an error while loading. Please reload this page.\n    \n*   Loading\n    \n    [Repository Layer](/dotnetcore/FreeSql/wiki/Repository-Layer)\n    \n    ### Uh oh!\n    \n    There was an error while loading. Please reload this page.\n    \n*   Loading\n    \n    [Return Data](/dotnetcore/FreeSql/wiki/Return-Data)\n    \n    ### Uh oh!\n    \n    There was an error while loading. Please reload this page.\n    \n*   Loading\n    \n    [Unit of Work](/dotnetcore/FreeSql/wiki/Unit-of-Work)\n    \n    ### Uh oh!\n    \n    There was an error while loading. Please reload this page.\n    \n*   Loading\n    \n    [Update Data](/dotnetcore/FreeSql/wiki/Update-Data)\n    \n    ### Uh oh!\n    \n    There was an error while loading. Please reload this page.\n    \n*   Loading\n    \n    [With Sql](/dotnetcore/FreeSql/wiki/With-Sql)\n    \n    ### Uh oh!\n    \n    There was an error while loading. Please reload this page.\n    \n*   Loading\n    \n    [withsql](/dotnetcore/FreeSql/wiki/withsql)\n    \n    ### Uh oh!\n    \n    There was an error while loading. Please reload this page.\n    \n*   Loading\n    \n    [事务](/dotnetcore/FreeSql/wiki/%E4%BA%8B%E5%8A%A1)\n    \n    ### Uh oh!\n    \n    There was an error while loading. Please reload this page.\n    \n*   Loading\n    \n    [修改](/dotnetcore/FreeSql/wiki/%E4%BF%AE%E6%94%B9)\n    \n    ### Uh oh!\n    \n    There was an error while loading. Please reload this page.\n    \n*   Loading\n    \n    [入门](/dotnetcore/FreeSql/wiki/%E5%85%A5%E9%97%A8)\n    \n    ### Uh oh!\n    \n    There was an error while loading. Please reload this page.\n    \n*   Loading\n    \n    [分组聚合查询](/dotnetcore/FreeSql/wiki/%E5%88%86%E7%BB%84%E8%81%9A%E5%90%88%E6%9F%A5%E8%AF%A2)\n    \n    ### Uh oh!\n    \n    There was an error while loading. Please reload this page.\n    \n*   Loading\n    \n    [分表分库](/dotnetcore/FreeSql/wiki/%E5%88%86%E8%A1%A8%E5%88%86%E5%BA%93)\n    \n    ### Uh oh!\n    \n    There was an error while loading. Please reload this page.\n    \n*   Loading\n    \n    [分页查询](/dotnetcore/FreeSql/wiki/%E5%88%86%E9%A1%B5%E6%9F%A5%E8%AF%A2)\n    \n    ### Uh oh!\n    \n    There was an error while loading. Please reload this page.\n    \n*   Loading\n    \n    [删除](/dotnetcore/FreeSql/wiki/%E5%88%A0%E9%99%A4)\n    \n    ### Uh oh!\n    \n    There was an error while loading. Please reload this page.\n    \n*   Loading\n    \n    [动态操作](/dotnetcore/FreeSql/wiki/%E5%8A%A8%E6%80%81%E6%93%8D%E4%BD%9C)\n    \n    ### Uh oh!\n    \n    There was an error while loading. Please reload this page.\n    \n*   Loading\n    \n    [单表查询](/dotnetcore/FreeSql/wiki/%E5%8D%95%E8%A1%A8%E6%9F%A5%E8%AF%A2)\n    \n    ### Uh oh!\n    \n    There was an error while loading. Please reload this page.\n    \n*   Loading\n    \n    [多表查询](/dotnetcore/FreeSql/wiki/%E5%A4%9A%E8%A1%A8%E6%9F%A5%E8%AF%A2)\n    \n    ### Uh oh!\n    \n    There was an error while loading. Please reload this page.\n    \n*   Loading\n    \n    [安装](/dotnetcore/FreeSql/wiki/%E5%AE%89%E8%A3%85)\n    \n    ### Uh oh!\n    \n    There was an error while loading. Please reload this page.\n    \n*   Loading\n    \n    [实体关系](/dotnetcore/FreeSql/wiki/%E5%AE%9E%E4%BD%93%E5%85%B3%E7%B3%BB)\n    \n    ### Uh oh!\n    \n    There was an error while loading. Please reload this page.\n    \n*   Loading\n    \n    [实体特性](/dotnetcore/FreeSql/wiki/%E5%AE%9E%E4%BD%93%E7%89%B9%E6%80%A7)\n    \n    ### Uh oh!\n    \n    There was an error while loading. Please reload this page.\n    \n*   Loading\n    \n    [导入数据库特性](/dotnetcore/FreeSql/wiki/%E5%AF%BC%E5%85%A5%E6%95%B0%E6%8D%AE%E5%BA%93%E7%89%B9%E6%80%A7)\n    \n    ### Uh oh!\n    \n    There was an error while loading. Please reload this page.\n    \n*   Loading\n    \n    [嵌套查询](/dotnetcore/FreeSql/wiki/%E5%B5%8C%E5%A5%97%E6%9F%A5%E8%AF%A2)\n    \n    ### Uh oh!\n    \n    There was an error while loading. Please reload this page.\n    \n*   Loading\n    \n    [工作单元](/dotnetcore/FreeSql/wiki/%E5%B7%A5%E4%BD%9C%E5%8D%95%E5%85%83)\n    \n    ### Uh oh!\n    \n    There was an error while loading. Please reload this page.\n    \n*   Loading\n    \n    [常见问题](/dotnetcore/FreeSql/wiki/%E5%B8%B8%E8%A7%81%E9%97%AE%E9%A2%98)\n    \n    ### Uh oh!\n    \n    There was an error while loading. Please reload this page.\n    \n*   Loading\n    \n    [延时加载](/dotnetcore/FreeSql/wiki/%E5%BB%B6%E6%97%B6%E5%8A%A0%E8%BD%BD)\n    \n    ### Uh oh!\n    \n    There was an error while loading. Please reload this page.\n    \n*   Loading\n    \n    [性能](/dotnetcore/FreeSql/wiki/%E6%80%A7%E8%83%BD)\n    \n    ### Uh oh!\n    \n    There was an error while loading. Please reload this page.\n    \n*   Loading\n    \n    [支持我们](/dotnetcore/FreeSql/wiki/%E6%94%AF%E6%8C%81%E6%88%91%E4%BB%AC)\n    \n    ### Uh oh!\n    \n    There was an error while loading. Please reload this page.\n    \n*   Loading\n    \n    [更新日志](/dotnetcore/FreeSql/wiki/%E6%9B%B4%E6%96%B0%E6%97%A5%E5%BF%97)\n    \n    ### Uh oh!\n    \n    There was an error while loading. Please reload this page.\n    \n*   Loading\n    \n    [查询](/dotnetcore/FreeSql/wiki/%E6%9F%A5%E8%AF%A2)\n    \n    ### Uh oh!\n    \n    There was an error while loading. Please reload this page.\n    \n*   Loading\n    \n    [查询父子关系](/dotnetcore/FreeSql/wiki/%E6%9F%A5%E8%AF%A2%E7%88%B6%E5%AD%90%E5%85%B3%E7%B3%BB)\n    \n    ### Uh oh!\n    \n    There was an error while loading. Please reload this page.\n    \n*   Loading\n    \n    [添加](/dotnetcore/FreeSql/wiki/%E6%B7%BB%E5%8A%A0)\n    \n    ### Uh oh!\n    \n    There was an error while loading. Please reload this page.\n    \n*   Loading\n    \n    [添加或修改](/dotnetcore/FreeSql/wiki/%E6%B7%BB%E5%8A%A0%E6%88%96%E4%BF%AE%E6%94%B9)\n    \n    ### Uh oh!\n    \n    There was an error while loading. Please reload this page.\n    \n*   Loading\n    \n    [租户](/dotnetcore/FreeSql/wiki/%E7%A7%9F%E6%88%B7)\n    \n    ### Uh oh!\n    \n    There was an error while loading. Please reload this page.\n    \n*   Loading\n    \n    [类型映射](/dotnetcore/FreeSql/wiki/%E7%B1%BB%E5%9E%8B%E6%98%A0%E5%B0%84)\n    \n    ### Uh oh!\n    \n    There was an error while loading. Please reload this page.\n    \n*   Loading\n    \n    [联合查询](/dotnetcore/FreeSql/wiki/%E8%81%94%E5%90%88%E6%9F%A5%E8%AF%A2)\n    \n    ### Uh oh!\n    \n    There was an error while loading. Please reload this page.\n    \n*   Loading\n    \n    [联级保存](/dotnetcore/FreeSql/wiki/%E8%81%94%E7%BA%A7%E4%BF%9D%E5%AD%98)\n    \n    ### Uh oh!\n    \n    There was an error while loading. Please reload this page.\n    \n*   Loading\n    \n    [联级删除](/dotnetcore/FreeSql/wiki/%E8%81%94%E7%BA%A7%E5%88%A0%E9%99%A4)\n    \n    ### Uh oh!\n    \n    There was an error while loading. Please reload this page.\n    \n*   Loading\n    \n    [聚合根（实验室）](/dotnetcore/FreeSql/wiki/%E8%81%9A%E5%90%88%E6%A0%B9%EF%BC%88%E5%AE%9E%E9%AA%8C%E5%AE%A4%EF%BC%89)\n    \n    ### Uh oh!\n    \n    There was an error while loading. Please reload this page.\n    \n*   Loading\n    \n    [自定义特性](/dotnetcore/FreeSql/wiki/%E8%87%AA%E5%AE%9A%E4%B9%89%E7%89%B9%E6%80%A7)\n    \n    ### Uh oh!\n    \n    There was an error while loading. Please reload this page.\n    \n*   Loading\n    \n    [表达式函数](/dotnetcore/FreeSql/wiki/%E8%A1%A8%E8%BE%BE%E5%BC%8F%E5%87%BD%E6%95%B0)\n    \n    ### Uh oh!\n    \n    There was an error while loading. Please reload this page.\n    \n*   Loading\n    \n    [读写分离](/dotnetcore/FreeSql/wiki/%E8%AF%BB%E5%86%99%E5%88%86%E7%A6%BB)\n    \n    ### Uh oh!\n    \n    There was an error while loading. Please reload this page.\n    \n*   Loading\n    \n    [贪婪加载](/dotnetcore/FreeSql/wiki/%E8%B4%AA%E5%A9%AA%E5%8A%A0%E8%BD%BD)\n    \n    ### Uh oh!\n    \n    There was an error while loading. Please reload this page.\n    \n*   Loading\n    \n    [过滤器](/dotnetcore/FreeSql/wiki/%E8%BF%87%E6%BB%A4%E5%99%A8)\n    \n    ### Uh oh!\n    \n    There was an error while loading. Please reload this page.\n    \n*   Loading\n    \n    [返回数据](/dotnetcore/FreeSql/wiki/%E8%BF%94%E5%9B%9E%E6%95%B0%E6%8D%AE)\n    \n    ### Uh oh!\n    \n    There was an error while loading. Please reload this page.\n    \n*   Loading\n    \n    [首页](/dotnetcore/FreeSql/wiki/%E9%A6%96%E9%A1%B5)\n    \n    ### Uh oh!\n    \n    There was an error while loading. Please reload this page.\n    \n*   Loading\n    \n    [骚操作](/dotnetcore/FreeSql/wiki/%E9%AA%9A%E6%93%8D%E4%BD%9C)\n    \n    ### Uh oh!\n    \n    There was an error while loading. Please reload this page.\n    \n*   Show 64 more pages…\n\nBasic\n-----\n\n[](#basic)\n\n*   [入门](%e5%85%a5%e9%97%a8) [Getting Started](Getting-Started)\n*   [安装](%e5%ae%89%e8%a3%85) [How to Install](Install)\n*   [添加](%e6%b7%bb%e5%8a%a0) [Insert Data](Insert-Data)\n    *   [单条](%e6%b7%bb%e5%8a%a0#1%E5%8D%95%E6%9D%A1%E6%8F%92%E5%85%A5) [Single](Insert-Data#1-single-insert)\n    *   [批量](%e6%b7%bb%e5%8a%a0#2%E6%89%B9%E9%87%8F%E6%8F%92%E5%85%A5) [Batch](Insert-Data#2-batch-insert)\n*   [删除](%e5%88%a0%e9%99%a4) [Delete Data](Delete-Data)\n*   [修改](%e4%bf%ae%e6%94%b9) [Update Data](Update-Data)\n    *   [更新实体](%e4%bf%ae%e6%94%b9#3%E6%9B%B4%E6%96%B0%E5%AE%9E%E4%BD%93) [Entity](Update-Data#3-update-the-entity)\n    *   [更新指定列](%e4%bf%ae%e6%94%b9#1%E6%9B%B4%E6%96%B0%E6%8C%87%E5%AE%9A%E5%88%97) [Specified Columns](Update-Data#1-update-the-specified-column)\n*   [添加或修改](%e6%b7%bb%e5%8a%a0%e6%88%96%e4%bf%ae%e6%94%b9) [Insert or Update](Insert-or-Update) ✨\n*   [查询](%e6%9f%a5%e8%af%a2) [Query Data](Query-Data)\n    *   [分页](%e5%88%86%e9%a1%b5%e6%9f%a5%e8%af%a2) [Pagination](Pagination)\n    *   [单表](%e5%8d%95%e8%a1%a8%e6%9f%a5%e8%af%a2) [Single Table](Query-from-Single-Table)\n    *   [多表](%e5%a4%9a%e8%a1%a8%e6%9f%a5%e8%af%a2) [Multi Tables](Query-from-Multi-Tables)\n    *   [分组聚合](%e5%88%86%e7%bb%84%e8%81%9a%e5%90%88%e6%9f%a5%e8%af%a2) [Group Aggregation](Group-Aggregation-Query)\n    *   [嵌套查询](%e5%b5%8c%e5%a5%97%e6%9f%a5%e8%af%a2) [Nested Query](Nested-Query) ✨\n    *   [联合查询](%E8%81%94%E5%90%88%E6%9F%A5%E8%AF%A2)\n    *   [返回数据](%e8%bf%94%e5%9b%9e%e6%95%b0%e6%8d%ae) [Return Data](Return-Data) ✨\n    *   [延时加载](%e5%bb%b6%e6%97%b6%e5%8a%a0%e8%bd%bd) [Lazy Loading](Lazy-Loading)\n    *   [贪婪加载](%e8%b4%aa%e5%a9%aa%e5%8a%a0%e8%bd%bd) [Greed Loading](Greed-Loading) ✨\n    *   [LINQ 扩展](LinqToSql) [LinqToSql](Linq-to-Sql)\n    *   [树型查询](%e6%9f%a5%e8%af%a2%e7%88%b6%e5%ad%90%e5%85%b3%e7%b3%bb) [Parent-Child Relp.](Parent-Child-Relationship-Query) ✨\n*   [仓储层](Repository) [Repository Layer](Repository-Layer)\n    *   [工作单元](%e5%b7%a5%e4%bd%9c%e5%8d%95%e5%85%83) [Unit of Work](Unit-of-Work)\n    *   [联级保存](%e8%81%94%e7%ba%a7%e4%bf%9d%e5%ad%98) [Cascade Saving](Cascade-Saving)\n    *   [联级删除](%E8%81%94%E7%BA%A7%E5%88%A0%E9%99%A4) [Cascade Deletion](Cascade-Deletion)\n    *   [工作单元管理器](DI-UnitOfWorkManager) ✨\n    *   [聚合根（实验室）](%E8%81%9A%E5%90%88%E6%A0%B9%EF%BC%88%E5%AE%9E%E9%AA%8C%E5%AE%A4%EF%BC%89)\n*   [CodeFirst](CodeFirst)\n    *   [实体特性✨](%e5%ae%9e%e4%bd%93%e7%89%b9%e6%80%a7)\n    *   [FluentApi](FluentApi)\n    *   [自定义特性](%e8%87%aa%e5%ae%9a%e4%b9%89%e7%89%b9%e6%80%a7)\n    *   [类型映射](%e7%b1%bb%e5%9e%8b%e6%98%a0%e5%b0%84)\n    *   [导航属性](%e5%ae%9e%e4%bd%93%e5%85%b3%e7%b3%bb) [Entity Relationship](Entity-Relationship) ✨\n    *   [迁移结构](CodeFirst#%e8%bf%81%e7%a7%bb%e7%bb%93%e6%9e%84)\n*   [DbFirst](DbFirst)\n*   [表达式函数](%e8%a1%a8%e8%be%be%e5%bc%8f%e5%87%bd%e6%95%b0)\n    *   [字符串](%e8%a1%a8%e8%be%be%e5%bc%8f%e5%87%bd%e6%95%b0#%e5%ad%97%e7%ac%a6%e4%b8%b2)\n    *   [日期/时间](%e8%a1%a8%e8%be%be%e5%bc%8f%e5%87%bd%e6%95%b0#%e6%97%a5%e6%9c%9f)\n    *   [其他](%e8%a1%a8%e8%be%be%e5%bc%8f%e5%87%bd%e6%95%b0#%e8%a1%a8%e8%be%be%e5%bc%8f%e5%87%bd%e6%95%b0%e5%85%a8%e8%a7%88)\n    *   [自定义函数](%e8%a1%a8%e8%be%be%e5%bc%8f%e5%87%bd%e6%95%b0#%E8%87%AA%E5%AE%9A%E4%B9%89%E8%A7%A3%E6%9E%90)\n*   [事务](%e4%ba%8b%e5%8a%a1)\n*   [过滤器](%e8%bf%87%e6%bb%a4%e5%99%a8)\n*   [ADO](ADO)\n*   [AOP✨](AOP)\n*   [读写分离](%e8%af%bb%e5%86%99%e5%88%86%e7%a6%bb)\n*   [分表分库](%e5%88%86%e8%a1%a8%e5%88%86%e5%ba%93)\n*   [租户](%e7%a7%9f%e6%88%b7)\n*   [性能](%e6%80%a7%e8%83%bd)\n*   [动态操作](%E5%8A%A8%E6%80%81%E6%93%8D%E4%BD%9C) [Dynamic Operations](Dynamic-Operations)\n*   [_你不知道的功能✨_](%E9%AA%9A%E6%93%8D%E4%BD%9C)\n*   [API参考](API)\n\n使用指南\n----\n\n[](#使用指南)\n\n*   [Dapper比较](Dapper%e6%af%94%e8%be%83)\n*   [EntityFramework比较](EntityFramework%e6%af%94%e8%be%83)\n\n学习与交流\n-----\n\n[](#学习与交流)\n\n*   [常见问题](%e5%b8%b8%e8%a7%81%e9%97%ae%e9%a2%98)\n*   [提交Issues](https://github.com/dotnetcore/FreeSql/issues/new/choose)\n\n信息\n--\n\n[](#信息)\n\n*   [服务支持](%E6%94%AF%E6%8C%81%E6%88%91%E4%BB%AC)\n*   [更新日志](%e6%9b%b4%e6%96%b0%e6%97%a5%e5%bf%97)\n\n##### Clone this wiki locally\n\nFooter\n------\n\n[](https://github.com)© 2025 GitHub, Inc.\n\n### Footer navigation\n\n*   [Terms](https://docs.github.com/site-policy/github-terms/github-terms-of-service)\n*   [Privacy](https://docs.github.com/site-policy/privacy-policies/github-privacy-statement)\n*   [Security](https://github.com/security)\n*   [Status](https://www.githubstatus.com/)\n*   [Docs](https://docs.github.com/)\n*   [Contact](https://support.github.com?tags=dotcom-footer)\n*   Manage cookies\n*   Do not share my personal information\n\nYou can’t perform that action at this time.&quot;,&quot;is_error&quot;:false,&quot;request_id&quot;:&quot;&quot;,&quot;tool_use_id&quot;:&quot;toolu_011mxat4LGz4UvpKT7D1T8tm&quot;}},{&quot;id&quot;:2,&quot;type&quot;:4,&quot;ide_state_node&quot;:{&quot;workspaceFolders&quot;:[{&quot;repositoryRoot&quot;:&quot;C:/Users/<USER>/Projects/OcuSimulator&quot;,&quot;folderRoot&quot;:&quot;C:/Users/<USER>/Projects/OcuSimulator&quot;}],&quot;workspaceFoldersUnchanged&quot;:false,&quot;currentTerminal&quot;:{&quot;terminalId&quot;:19,&quot;currentWorkingDirectory&quot;:&quot;C:/Users/<USER>/Projects/OcuSimulator&quot;}}}],&quot;timestamp&quot;:&quot;2025-07-08T06:58:41.497Z&quot;,&quot;response_text&quot;:&quot;基于我对代码的分析和FreeSql文档的查阅，我来为您详细解释`ExecuteIdentity`和普通`Execute`的区别，以及`Column(Identity=True)`的作用：\n\n## ExecuteIdentity vs Execute 的区别\n\n### 1. **ExecuteIdentity**\n- **用途**：专门用于插入数据后返回**自增主键的值**\n- **返回值**：`long` 类型，返回数据库生成的自增ID\n- **使用场景**：当表有自增主键时，插入数据后需要获取生成的ID值\n\n````csharp path=src/Simulator.Server/Controllers/UserController.cs mode=EXCERPT\nsql.Insert(astudentbutmaybeateacher).ExecuteIdentity();\n````\n\n### 2. **ExecuteAffrows** (常见的Execute方法)\n- **用途**：执行SQL并返回**受影响的行数**\n- **返回值**：`long` 类型，返回受影响的行数\n- **使用场景**：当你只需要知道操作是否成功，不需要获取自增ID时\n\n```csharp\n// 返回受影响的行数\nlong affectedRows = sql.Insert(user).ExecuteAffrows();\n```\n\n### 3. **ExecuteInserted**\n- **用途**：执行SQL并返回**插入后的完整记录**\n- **返回值**：`List&lt;T&gt;` 类型，包含插入后的完整实体（包括自增ID）\n- **使用场景**：需要获取插入后的完整记录信息\n\n## Column(Identity=True) 的作用\n\n`[Column(Identity=True)]` 是FreeSql中的特性标记，用于：\n\n### 1. **标识自增列**\n```csharp\nclass Topic {\n    [Column(IsIdentity = true, IsPrimary = true)]\n    public int Id { get; set; }\n    public string Title { get; set; }\n}\n```\n\n### 2. **主要作用**：\n- **告诉FreeSql这是一个自增主键列**\n- **插入时自动跳过该列**（数据库会自动生成值）\n- **使用ExecuteIdentity时能正确返回生成的ID值**\n- **批量插入时能正确回填自增ID到实体对象**\n\n### 3. **数据库行为**：\n- 插入时SQL不会包含该列\n- 数据库自动生成递增的数值\n- ExecuteIdentity能获取到数据库生成的值\n\n## 您当前代码的问题\n\n查看您的User模型：\n\n````csharp path=src/Simulator.Server/Models/User.cs mode=EXCERPT\npublic class User\n{\n    [Key] public required string Id { get; init; }\n    public required string Username { get; init; }\n    public required string Password { get; init; }\n    public required bool IsTeacher { get; init; }\n}\n````\n\n**问题**：您的`Id`是`string`类型且手动赋值（使用Nanoid），但在Controller中使用了`ExecuteIdentity()`：\n\n````csharp path=src/Simulator.Server/Controllers/UserController.cs mode=EXCERPT\nvar astudentbutmaybeateacher = new User\n{\n    Id = Nanoid.Generate(), // 手动生成ID\n    Username = username,\n    Password = password,\n    IsTeacher = isTeacher\n};\n\nsql.Insert(astudentbutmaybeateacher).ExecuteIdentity(); // 这里不合适\n````\n\n## 建议的修改\n\n由于您使用的是手动生成的字符串ID，应该使用`ExecuteAffrows()`而不是`ExecuteIdentity()`：\n\n```csharp\n// 推荐做法\nvar result = sql.Insert(astudentbutmaybeateacher).ExecuteAffrows();\nif (result &gt; 0)\n{\n    return Ok(astudentbutmaybeateacher);\n}\nelse\n{\n    return BadRequest(\&quot;插入失败\&quot;);\n}\n```\n\n或者如果您想改为使用自增整数ID：\n\n```csharp\npublic class User\n{\n    [Column(IsIdentity = true, IsPrimary = true)]\n    public int Id { get; set; } // 改为int自增\n    public required string Username { get; init; }\n    public required string Password { get; init; }\n    public required bool IsTeacher { get; init; }\n}\n\n// Controller中\nvar user = new User\n{\n    // 不设置Id，让数据库自动生成\n    Username = username,\n    Password = password,\n    IsTeacher = isTeacher\n};\n\nlong newId = sql.Insert(user).ExecuteIdentity();\nuser.Id = (int)newId; // 回填ID\n```\n\n总结：`ExecuteIdentity`专门用于获取数据库自增主键，而您当前使用手动生成的字符串ID，所以应该使用`ExecuteAffrows()`。\n&quot;,&quot;workspace_file_chunks&quot;:[]}],&quot;feedbackStates&quot;:{&quot;temp-fe-99db5c9e-1bdf-4c9f-b2ff-5835101887dd&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;},&quot;temp-fe-5ad8a86d-97a8-4b1c-82de-56476c0fb226&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;},&quot;temp-fe-9ec9ed08-3d60-4c81-b06c-55435dd5596b&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;},&quot;temp-fe-524aaa39-3e6e-4ba0-a618-6f9f910ba069&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;},&quot;temp-fe-3b01ff0b-b69e-44c2-b29e-5706377cb119&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;},&quot;temp-fe-d9a626b2-acf6-4f82-a5ba-af81c9ace159&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;}},&quot;toolUseStates&quot;:{&quot;0d763763-0075-4f4e-a178-c306937c58ac;toolu_01ABhGDpc7xp386iqWAnvikm&quot;:{&quot;requestId&quot;:&quot;0d763763-0075-4f4e-a178-c306937c58ac&quot;,&quot;toolUseId&quot;:&quot;toolu_01ABhGDpc7xp386iqWAnvikm&quot;,&quot;phase&quot;:5,&quot;result&quot;:{&quot;text&quot;:&quot;Note: Path was automatically corrected from 'src/Simulator.Server/Controllers/UserController.cs' to 'C:\\Users\\<USER>\\Projects\\OcuSimulator\\src\\Simulator.Server\\Controllers\\UserController.cs'.\n\nHere's the result of running `cat -n` on C:\\Users\\<USER>\\Projects\\OcuSimulator\\src\\Simulator.Server\\Controllers\\UserController.cs:\n     1\t﻿using Microsoft.AspNetCore.Mvc;\n     2\tusing NanoidDotNet;\n     3\tusing Simulator.Server.Models;\n     4\t\n     5\tnamespace Simulator.Server.Controllers;\n     6\t\n     7\t[ApiController]\n     8\t[Route(\&quot;api/[controller]\&quot;)]\n     9\tpublic class UserController(IFreeSql sql) : Controller\n    10\t{\n    11\t    [HttpPost(\&quot;login\&quot;)]\n    12\t    public IActionResult Login(string username, string password)\n    13\t    {\n    14\t        var user = sql.Select&lt;User&gt;().Where(u =&gt; u.Username == username &amp;&amp; u.Password == password).ToOne();\n    15\t        if (user == null)\n    16\t        {\n    17\t            return Unauthorized();\n    18\t        }\n    19\t\n    20\t        return Ok(user);\n    21\t    }\n    22\t\n    23\t    public IActionResult Register(string username, string password, bool isTeacher)\n    24\t    {\n    25\t        var astudentbutmaybeateacher = new User\n    26\t        {\n    27\t            Id = Nanoid.Generate(),\n    28\t            Username = username,\n    29\t            Password = password,\n    30\t            IsTeacher = isTeacher\n    31\t        };\n    32\t\n    33\t        sql.Insert(astudentbutmaybeateacher).ExecuteIdentity();\n    34\t\n    35\t        return Ok(astudentbutmaybeateacher);\n    36\t    }\n    37\t}\nTotal lines in file: 37\n&quot;,&quot;isError&quot;:false,&quot;requestId&quot;:&quot;&quot;}},&quot;dd5161ee-0eef-4c9b-b08b-3b01b6ff2391;toolu_0146tG1aZwyfYpzxTFSEF74b&quot;:{&quot;requestId&quot;:&quot;dd5161ee-0eef-4c9b-b08b-3b01b6ff2391&quot;,&quot;toolUseId&quot;:&quot;toolu_0146tG1aZwyfYpzxTFSEF74b&quot;,&quot;phase&quot;:5,&quot;result&quot;:{&quot;text&quot;:&quot;Note: Path was automatically corrected from 'src/Simulator.Server/Models/User.cs' to 'C:\\Users\\<USER>\\Projects\\OcuSimulator\\src\\Simulator.Server\\Models\\User.cs'.\n\nHere's the result of running `cat -n` on C:\\Users\\<USER>\\Projects\\OcuSimulator\\src\\Simulator.Server\\Models\\User.cs:\n     1\t﻿using System.ComponentModel.DataAnnotations;\n     2\t\n     3\tnamespace Simulator.Server.Models;\n     4\t\n     5\tpublic class User\n     6\t{\n     7\t    [Key] public required string Id { get; init; }\n     8\t    public required string Username { get; init; }\n     9\t    public required string Password { get; init; }\n    10\t    public required bool IsTeacher { get; init; }\n    11\t}\nTotal lines in file: 11\n&quot;,&quot;isError&quot;:false,&quot;requestId&quot;:&quot;&quot;}},&quot;ac885dd9-587d-44cf-932d-62be2644bf64;toolu_01NBbDSn4Dk6nnwyjq3ESvPc&quot;:{&quot;requestId&quot;:&quot;ac885dd9-587d-44cf-932d-62be2644bf64&quot;,&quot;toolUseId&quot;:&quot;toolu_01NBbDSn4Dk6nnwyjq3ESvPc&quot;,&quot;phase&quot;:5,&quot;result&quot;:{&quot;text&quot;:&quot;The following code sections were retrieved:\nPath: docs/README_FreeSql.md\n     1\t# FreeSql 集成说明\n     2\t\n     3\t本项目已成功集成 FreeSql 并配置使用 SQLite 数据库。\n     4\t\n     5\t## 已安装的包\n     6\t\n     7\t- `FreeSql` - FreeSql 核心包\n     8\t- `FreeSql.Provider.Sqlite` - SQLite 数据库提供程序\n     9\t\n    10\t## 项目结构\n    11\t\n    12\t```\n    13\tsrc/Simulator.Server/\n    14\t├── Data/\n    15\t│   ├── FreeSqlExtensions.cs         # FreeSql 配置和扩展方法\n    16\t│   └── Question.cs                  # 其他实体模型\n    17\t├── Models/\n    18\t│   └── User.cs                      # 示例实体模型\n    19\t├── Controllers/\n    20\t│   └── UsersController.cs           # 示例 API 控制器\n    21\t├── appsettings.json                 # 生产环境配置\n    22\t├── appsettings.Development.json     # 开发环境配置\n    23\t└── simulator_dev.db                 # SQLite 数据库文件（开发环境）\n    24\t```\n    25\t\n    26\t## 数据库配置\n    27\t\n    28\t### 连接字符串\n    29\t\n    30\t- **开发环境**: `Data Source=simulator_dev.db`\n    31\t- **生产环境**: `Data Source=simulator.db`\n    32\t\n    33\t### FreeSql 配置\n    34\t\n    35\tFreeSql 配置位于 `Data/FreeSqlExtensions.cs`，包含：\n    36\t- 数据库连接配置\n    37\t- 自动同步结构设置\n    38\t- SQL 监控配置\n    39\t\n    40\t## 使用示例\n    41\t\n    42\t### 1. 添加新实体\n    43\t\n    44\t1. 在 `Models/` 目录下创建实体类\n    45\t2. 使用 FreeSql 注解配置实体（如 `[Table]`, `[Column]` 等）\n    46\t3. 在 `FreeSqlExtensions.cs` 的 `EnsureDatabaseCreatedAsync` 方法中添加 `freeSql.CodeFirst.SyncStructure&lt;YourEntity&gt;()`\n    47\t4. FreeSql 会自动创建或更新数据库结构\n    48\t\n    49\t### 2. 在控制器中使用\n    50\t\n    51\t```csharp\n    52\t[ApiController]\n    53\t[Route(\&quot;api/[controller]\&quot;)]\n    54\tpublic class YourController : ControllerBase\n    55\t{\n    56\t    private readonly IFreeSql _freeSql;\n    57\t\n    58\t    public YourController(IFreeSql freeSql)\n    59\t    {\n    60\t        _freeSql = freeSql;\n    61\t    }\n    62\t\n    63\t    [HttpGet]\n    64\t    public async Task&lt;ActionResult&lt;IEnumerable&lt;YourEntity&gt;&gt;&gt; Get()\n    65\t    {\n    66\t        var entities = await _freeSql.Select&lt;YourEntity&gt;().ToListAsync();\n    67\t        return Ok(entities);\n    68\t    }\n    69\t}\n    70\t```\n    71\t\n    72\t### 3. 在服务中使用\n    73\t\n    74\t```csharp\n    75\tpublic class YourService\n    76\t{\n    77\t    private readonly IFreeSql _freeSql;\n    78\t\n    79\t    public YourService(IFreeSql freeSql)\n    80\t    {\n    81\t        _freeSql = freeSql;\n    82\t    }\n    83\t\n    84\t    public async Task&lt;List&lt;YourEntity&gt;&gt; GetAllAsync()\n    85\t    {\n    86\t        return await _freeSql.Select&lt;YourEntity&gt;().ToListAsync();\n    87\t    }\n    88\t}\n    89\t```\n    90\t\n    91\t## FreeSql 常用操作\n    92\t\n    93\t```csharp\n    94\t// 查询\n    95\tvar users = await freeSql.Select&lt;User&gt;().ToListAsync();\n    96\tvar user = await freeSql.Select&lt;User&gt;().Where(u =&gt; u.Id == 1).FirstAsync();\n    97\t\n    98\t// 插入\n    99\tvar newUser = new User { Name = \&quot;张三\&quot;, Email = \&quot;<EMAIL>\&quot; };\n   100\tawait freeSql.Insert(newUser).ExecuteAffrowsAsync();\n   101\t\n   102\t// 更新\n   103\tawait freeSql.Update&lt;User&gt;()\n   104\t    .Set(u =&gt; u.Name, \&quot;李四\&quot;)\n   105\t    .Where(u =&gt; u.Id == 1)\n   106\t    .ExecuteAffrowsAsync();\n   107\t\n   108\t// 删除\n   109\tawait freeSql.Delete&lt;User&gt;().Where(u =&gt; u.Id == 1).ExecuteAffrowsAsync();\n   110\t\n   111\t// 同步数据库结构\n   112\tfreeSql.CodeFirst.SyncStructure&lt;User&gt;();\n   113\t```\n   114\t\n   115\t## API 端点示例\n   116\t\n   117\t应用程序包含一个示例 Users API：\n   118\t\n   119\t- `GET /api/users` - 获取所有用户\n   120\t- `GET /api/users/{id}` - 获取指定用户\n   121\t- `POST /api/users` - 创建新用户\n   122\t- `PUT /api/users/{id}` - 更新用户\n   123\t- `DELETE /api/users/{id}` - 删除用户\n   124\t\n   125\t## 数据库初始化\n   126\t\n   127\t应用程序启动时会自动：\n   128\t1. 同步数据库结构（创建或更新表）\n   129\t2. 在开发环境中添加示例数据（如果数据库为空）\n   130\t\n   131\t这通过 `Program.cs` 中的以下代码实现：\n   132\t\n   133\t```csharp\n   134\tawait app.EnsureDatabaseCreatedAsync();\n   135\tif (app.Environment.IsDevelopment())\n   136\t{\n   137\t    await app.SeedDatabaseAsync();\n   138\t}\n   139\t```\n   140\t\n   141\t## 注意事项\n   142\t\n   143\t1. SQLite 数据库文件会在项目根目录下创建\n   144\t2. 开发环境和生产环境使用不同的数据库文件\n   145\t3. 迁移文件应该提交到版本控制系统\n   146\t4. 数据库文件可以添加到 `.gitignore` 中（如果不需要共享数据）\n...\nPath: src/Simulator.Server/Data/FreeSqlExtensions.cs\n     1\tusing FreeSql;\n     2\tusing Simulator.Server.Models;\n     3\t\n     4\tnamespace Simulator.Server.Data\n     5\t{\n     6\t    public static class FreeSqlExtensions\n     7\t    {\n     8\t        public static IServiceCollection AddFreeSql(this IServiceCollection services, IConfiguration configuration)\n     9\t        {\n    10\t            services.AddSingleton&lt;IFreeSql&gt;(_ =&gt; new FreeSqlBuilder()\n    11\t                .UseConnectionString(DataType.Sqlite, \&quot;Data Source=Simulator.db;Cache=Private\&quot;)\n    12\t                .UseAutoSyncStructure(true)\n    13\t                .Build());\n    14\t\n    15\t\n    16\t            return services;\n    17\t        }\n    18\t    }\n    19\t}...\nPath: src/Simulator.Server/README_EFCore.md\n     1\t# Entity Framework Core 集成说明\n     2\t\n     3\t本项目已成功集成 Entity Framework Core 并配置使用 SQLite 数据库。\n     4\t\n     5\t## 已安装的包\n     6\t\n     7\t- `Microsoft.EntityFrameworkCore.Sqlite` - SQLite 数据库提供程序\n     8\t- `Microsoft.EntityFrameworkCore.Tools` - EF Core 工具（用于迁移等）\n     9\t\n    10\t## 项目结构\n    11\t\n    12\t```\n    13\tsrc/Simulator.Server/\n    14\t├── Data/\n    15\t│   ├── ApplicationDbContext.cs      # 数据库上下文\n    16\t│   └── DatabaseExtensions.cs       # 数据库扩展方法\n    17\t├── Models/\n    18\t│   └── User.cs                      # 示例实体模型\n    19\t├── Controllers/\n    20\t│   └── UsersController.cs           # 示例 API 控制器\n    21\t├── Migrations/                      # EF Core 迁移文件\n    22\t├── appsettings.json                 # 生产环境配置\n    23\t├── appsettings.Development.json     # 开发环境配置\n    24\t└── simulator_dev.db                 # SQLite 数据库文件（开发环境）\n    25\t```\n    26\t\n    27\t## 数据库配置\n    28\t\n    29\t### 连接字符串\n    30\t\n    31\t- **开发环境**: `Data Source=simulator_dev.db`\n    32\t- **生产环境**: `Data Source=simulator.db`\n    33\t\n    34\t### 数据库上下文\n    35\t\n    36\t`ApplicationDbContext` 类位于 `Data/ApplicationDbContext.cs`，包含：\n    37\t- 实体配置\n    38\t- 关系映射\n    39\t- 约束定义\n    40\t\n    41\t## 使用示例\n    42\t\n    43\t### 1. 添加新实体\n    44\t\n    45\t1. 在 `Models/` 目录下创建实体类\n    46\t2. 在 `ApplicationDbContext` 中添加 `DbSet&lt;T&gt;` 属性\n    47\t3. 在 `OnModelCreating` 方法中配置实体关系\n    48\t4. 创建迁移：`dotnet ef migrations add &lt;MigrationName&gt;`\n    49\t5. 应用迁移：`dotnet ef database update`\n    50\t\n    51\t### 2. 在控制器中使用\n    52\t\n    53\t```csharp\n    54\t[ApiController]\n    55\t[Route(\&quot;api/[controller]\&quot;)]\n    56\tpublic class YourController : ControllerBase\n    57\t{\n    58\t    private readonly ApplicationDbContext _context;\n    59\t\n    60\t    public YourController(ApplicationDbContext context)\n    61\t    {\n    62\t        _context = context;\n    63\t    }\n    64\t\n    65\t    [HttpGet]\n    66\t    public async Task&lt;ActionResult&lt;IEnumerable&lt;YourEntity&gt;&gt;&gt; Get()\n    67\t    {\n    68\t        return await _context.YourEntities.ToListAsync();\n    69\t    }\n    70\t}\n...\n    90\t```\n    91\t\n    92\t## 常用 EF Core 命令\n    93\t\n    94\t```bash\n    95\t# 创建迁移\n    96\tdotnet ef migrations add &lt;MigrationName&gt;\n    97\t\n    98\t# 应用迁移\n    99\tdotnet ef database update\n   100\t\n   101\t# 回滚到指定迁移\n   102\tdotnet ef database update &lt;MigrationName&gt;\n   103\t\n   104\t# 删除最后一个迁移\n   105\tdotnet ef migrations remove\n   106\t\n   107\t# 查看迁移状态\n   108\tdotnet ef migrations list\n   109\t\n   110\t# 生成 SQL 脚本\n   111\tdotnet ef migrations script\n   112\t```\n   113\t\n   114\t## API 端点示例\n   115\t\n   116\t应用程序包含一个示例 Users API：\n   117\t\n   118\t- `GET /api/users` - 获取所有用户\n   119\t- `GET /api/users/{id}` - 获取指定用户\n   120\t- `POST /api/users` - 创建新用户\n   121\t- `PUT /api/users/{id}` - 更新用户\n   122\t- `DELETE /api/users/{id}` - 删除用户\n   123\t\n   124\t## 数据库初始化\n   125\t\n   126\t应用程序启动时会自动：\n   127\t1. 应用所有待处理的迁移\n   128\t2. 在开发环境中添加示例数据（如果数据库为空）\n   129\t\n   130\t这通过 `Program.cs` 中的以下代码实现：\n   131\t\n   132\t```csharp\n   133\tawait app.EnsureDatabaseCreatedAsync();\n   134\tif (app.Environment.IsDevelopment())\n   135\t{\n   136\t    await app.SeedDatabaseAsync();\n   137\t}\n...\nPath: src/Simulator.Server/Startup.cs\n     1\tusing FreeSql;\n     2\tusing Mapster;\n     3\t\n     4\tnamespace Simulator.Server\n     5\t{\n     6\t    public static class Startup\n     7\t    {\n     8\t        public static void ConfigureServices(IServiceCollection services, IConfiguration configuration)\n     9\t        {\n    10\t            services.AddSingleton&lt;IFreeSql&gt;(_ =&gt; new FreeSqlBuilder()\n    11\t                .UseAdoConnectionPool(true)\n    12\t                .UseConnectionString(DataType.Sqlite, \&quot;Data Source=Simulator.db;Cache=Private\&quot;)\n    13\t                .UseAutoSyncStructure(true)\n    14\t                .Build());\n    15\t\n    16\t            services.AddMapster();\n    17\t        }\n    18\t    }\n    19\t}...\nPath: src/Simulator.Server/Migrations/20250707083248_InitialCreate.cs\n...\n     5\t\n     6\tnamespace Simulator.Server.Migrations\n     7\t{\n     8\t    /// &lt;inheritdoc /&gt;\n     9\t    public partial class InitialCreate : Migration\n    10\t    {\n    11\t        /// &lt;inheritdoc /&gt;\n    12\t        protected override void Up(MigrationBuilder migrationBuilder)\n    13\t        {\n    14\t            migrationBuilder.CreateTable(\n    15\t                name: \&quot;Users\&quot;,\n    16\t                columns: table =&gt; new\n    17\t                {\n    18\t                    Id = table.Column&lt;int&gt;(type: \&quot;INTEGER\&quot;, nullable: false)\n    19\t                        .Annotation(\&quot;Sqlite:Autoincrement\&quot;, true),\n    20\t                    Name = table.Column&lt;string&gt;(type: \&quot;TEXT\&quot;, maxLength: 100, nullable: false),\n    21\t                    Email = table.Column&lt;string&gt;(type: \&quot;TEXT\&quot;, maxLength: 255, nullable: false),\n    22\t                    CreatedAt = table.Column&lt;DateTime&gt;(type: \&quot;TEXT\&quot;, nullable: false, defaultValueSql: \&quot;datetime('now')\&quot;),\n    23\t                    UpdatedAt = table.Column&lt;DateTime&gt;(type: \&quot;TEXT\&quot;, nullable: true)\n    24\t                },\n    25\t                constraints: table =&gt;\n    26\t                {\n    27\t                    table.PrimaryKey(\&quot;PK_Users\&quot;, x =&gt; x.Id);\n    28\t                });\n...\nPath: src/Simulator.Server/Program.cs\n     1\tusing Microsoft.AspNetCore.Authentication.JwtBearer;\n     2\tusing Microsoft.IdentityModel.Tokens;\n     3\tusing Simulator.Server;\n     4\t\n     5\tvar builder = WebApplication.CreateBuilder(args);\n     6\t\n     7\t// Add services to the container.\n     8\tbuilder.Services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme);\n...\nPath: src/Simulator.Server/Models/User.cs\n     1\tusing System.ComponentModel.DataAnnotations;\n     2\t\n     3\tnamespace Simulator.Server.Models;\n     4\t\n     5\tpublic class User\n     6\t{\n     7\t    [Key] public required string Id { get; init; }\n     8\t    public required string Username { get; init; }\n     9\t    public required string Password { get; init; }\n    10\t    public required bool IsTeacher { get; init; }\n    11\t}...\nPath: src/Simulator.Desktop/app.manifest\n     1\t&lt;?xml version=\&quot;1.0\&quot; encoding=\&quot;utf-8\&quot;?&gt;\n     2\t&lt;assembly manifestVersion=\&quot;1.0\&quot; xmlns=\&quot;urn:schemas-microsoft-com:asm.v1\&quot;&gt;\n     3\t  &lt;!-- This manifest is used on Windows only.\n     4\t       Don't remove it as it might cause problems with window transparency and embedded controls.\n     5\t       For more details visit https://learn.microsoft.com/en-us/windows/win32/sbscs/application-manifests --&gt;\n     6\t  &lt;assemblyIdentity version=\&quot;1.0.0.0\&quot; name=\&quot;Simulator.Desktop.Desktop\&quot;/&gt;\n...\nPath: src/Simulator.Server/Migrations/ApplicationDbContextModelSnapshot.cs\n...\n    19\t\n    20\t            modelBuilder.Entity(\&quot;Simulator.Server.Models.User\&quot;, b =&gt;\n    21\t                {\n    22\t                    b.Property&lt;int&gt;(\&quot;Id\&quot;)\n    23\t                        .ValueGeneratedOnAdd()\n    24\t                        .HasColumnType(\&quot;INTEGER\&quot;);\n    25\t\n    26\t                    b.Property&lt;DateTime&gt;(\&quot;CreatedAt\&quot;)\n    27\t                        .ValueGeneratedOnAdd()\n    28\t                        .HasColumnType(\&quot;TEXT\&quot;)\n    29\t                        .HasDefaultValueSql(\&quot;datetime('now')\&quot;);\n    30\t\n    31\t                    b.Property&lt;string&gt;(\&quot;Email\&quot;)\n    32\t                        .IsRequired()\n    33\t                        .HasMaxLength(255)\n    34\t                        .HasColumnType(\&quot;TEXT\&quot;);\n    35\t\n    36\t                    b.Property&lt;string&gt;(\&quot;Name\&quot;)\n    37\t                        .IsRequired()\n    38\t                        .HasMaxLength(100)\n    39\t                        .HasColumnType(\&quot;TEXT\&quot;);\n    40\t\n    41\t                    b.Property&lt;DateTime?&gt;(\&quot;UpdatedAt\&quot;)\n    42\t                        .HasColumnType(\&quot;TEXT\&quot;);\n    43\t\n    44\t                    b.HasKey(\&quot;Id\&quot;);\n    45\t\n    46\t                    b.HasIndex(\&quot;Email\&quot;)\n    47\t                        .IsUnique();\n    48\t\n    49\t                    b.ToTable(\&quot;Users\&quot;);\n    50\t                });\n    51\t#pragma warning restore 612, 618\n    52\t        }\n    53\t    }\n    54\t}\n...\nPath: src/Simulator.Server/Controllers/UsersController.cs\n     1\tusing Microsoft.AspNetCore.Mvc;\n     2\tusing FreeSql;\n     3\tusing Simulator.Server.Models;\n     4\t\n     5\tnamespace Simulator.Server.Controllers\n     6\t{\n     7\t    [ApiController]\n     8\t    [Route(\&quot;api/[controller]\&quot;)]\n     9\t    public class UsersController : ControllerBase\n    10\t    {\n    11\t        private readonly IFreeSql _freeSql;\n    12\t\n    13\t        public UsersController(IFreeSql freeSql)\n    14\t        {\n    15\t            _freeSql = freeSql;\n    16\t        }\n    17\t\n    18\t        // GET: api/users\n    19\t        [HttpGet]\n    20\t        public async Task&lt;ActionResult&lt;IEnumerable&lt;User&gt;&gt;&gt; GetUsers()\n    21\t        {\n    22\t            var users = await _freeSql.Select&lt;User&gt;().ToListAsync();\n    23\t            return Ok(users);\n    24\t        }\n    25\t\n    26\t        // GET: api/users/5\n    27\t        [HttpGet(\&quot;{id}\&quot;)]\n    28\t        public async Task&lt;ActionResult&lt;User&gt;&gt; GetUser(int id)\n    29\t        {\n    30\t            var user = await _freeSql.Select&lt;User&gt;().Where(u =&gt; u.Id == id).FirstAsync();\n    31\t\n    32\t            if (user == null)\n    33\t            {\n    34\t                return NotFound();\n    35\t            }\n    36\t\n    37\t            return user;\n    38\t        }\n    39\t\n    40\t        // POST: api/users\n    41\t        [HttpPost]\n    42\t        public async Task&lt;ActionResult&lt;User&gt;&gt; PostUser(User user)\n    43\t        {\n    44\t            user.CreatedAt = DateTime.Now;\n    45\t            var insertedUsers = await _freeSql.Insert(user).ExecuteInsertedAsync();\n    46\t            var insertedUser = insertedUsers.FirstOrDefault();\n    47\t\n    48\t            if (insertedUser == null)\n    49\t            {\n    50\t                return BadRequest(\&quot;Failed to create user\&quot;);\n    51\t            }\n    52\t\n    53\t            return CreatedAtAction(nameof(GetUser), new { id = insertedUser.Id }, insertedUser);\n    54\t        }\n    55\t\n    56\t        // PUT: api/users/5\n    57\t        [HttpPut(\&quot;{id}\&quot;)]\n    58\t        public async Task&lt;IActionResult&gt; PutUser(int id, User user)\n    59\t        {\n    60\t            if (id != user.Id)\n    61\t            {\n    62\t                return BadRequest();\n    63\t            }\n    64\t\n    65\t            user.UpdatedAt = DateTime.Now;\n    66\t            \n    67\t            var affectedRows = await _freeSql.Update&lt;User&gt;()\n    68\t                .SetSource(user)\n    69\t                .Where(u =&gt; u.Id == id)\n    70\t                .ExecuteAffrowsAsync();\n    71\t\n    72\t            if (affectedRows == 0)\n    73\t            {\n    74\t                return NotFound();\n    75\t            }\n    76\t\n    77\t            return NoContent();\n    78\t        }\n    79\t\n    80\t        // DELETE: api/users/5\n    81\t        [HttpDelete(\&quot;{id}\&quot;)]\n    82\t        public async Task&lt;IActionResult&gt; DeleteUser(int id)\n    83\t        {\n    84\t            var affectedRows = await _freeSql.Delete&lt;User&gt;()\n    85\t                .Where(u =&gt; u.Id == id)\n    86\t                .ExecuteAffrowsAsync();\n    87\t\n    88\t            if (affectedRows == 0)\n    89\t            {\n    90\t                return NotFound();\n    91\t            }\n    92\t\n    93\t            return NoContent();\n    94\t        }\n    95\t\n    96\t        private async Task&lt;bool&gt; UserExists(int id)\n    97\t        {\n    98\t            return await _freeSql.Select&lt;User&gt;().Where(u =&gt; u.Id == id).AnyAsync();\n    99\t        }\n   100\t    }\n   101\t}\n...\nPath: src/Simulator.Server/Migrations/20250707083248_InitialCreate.Designer.cs\n...\n    22\t\n    23\t            modelBuilder.Entity(\&quot;Simulator.Server.Models.User\&quot;, b =&gt;\n    24\t                {\n    25\t                    b.Property&lt;int&gt;(\&quot;Id\&quot;)\n    26\t                        .ValueGeneratedOnAdd()\n    27\t                        .HasColumnType(\&quot;INTEGER\&quot;);\n    28\t\n    29\t                    b.Property&lt;DateTime&gt;(\&quot;CreatedAt\&quot;)\n    30\t                        .ValueGeneratedOnAdd()\n    31\t                        .HasColumnType(\&quot;TEXT\&quot;)\n    32\t                        .HasDefaultValueSql(\&quot;datetime('now')\&quot;);\n    33\t\n    34\t                    b.Property&lt;string&gt;(\&quot;Email\&quot;)\n    35\t                        .IsRequired()\n    36\t                        .HasMaxLength(255)\n    37\t                        .HasColumnType(\&quot;TEXT\&quot;);\n    38\t\n    39\t                    b.Property&lt;string&gt;(\&quot;Name\&quot;)\n    40\t                        .IsRequired()\n    41\t                        .HasMaxLength(100)\n    42\t                        .HasColumnType(\&quot;TEXT\&quot;);\n    43\t\n    44\t                    b.Property&lt;DateTime?&gt;(\&quot;UpdatedAt\&quot;)\n    45\t                        .HasColumnType(\&quot;TEXT\&quot;);\n    46\t\n    47\t                    b.HasKey(\&quot;Id\&quot;);\n    48\t\n    49\t                    b.HasIndex(\&quot;Email\&quot;)\n    50\t                        .IsUnique();\n    51\t\n    52\t                    b.ToTable(\&quot;Users\&quot;);\n    53\t                });\n    54\t#pragma warning restore 612, 618\n    55\t        }\n    56\t    }\n    57\t}\n...\nPath: src/Simulator.Server/Data/ApplicationDbContext.cs\n     1\tusing Microsoft.EntityFrameworkCore;\n     2\tusing Simulator.Server.Models;\n     3\t\n     4\tnamespace Simulator.Server.Data\n     5\t{\n     6\t    public class ApplicationDbContext : DbContext\n     7\t    {\n     8\t        public ApplicationDbContext(DbContextOptions&lt;ApplicationDbContext&gt; options)\n     9\t            : base(options)\n    10\t        {\n    11\t        }\n    12\t\n    13\t        // DbSet 属性\n    14\t        public DbSet&lt;User&gt; Users { get; set; }\n    15\t\n    16\t        protected override void OnModelCreating(ModelBuilder modelBuilder)\n    17\t        {\n    18\t            base.OnModelCreating(modelBuilder);\n    19\t        }\n    20\t    }\n    21\t}\n...\nPath: src/Simulator.Server/Data/DatabaseExtensions.cs\n...\n    22\t\n    23\t        /// &lt;summary&gt;\n    24\t        /// 种子数据（可选）\n    25\t        /// &lt;/summary&gt;\n    26\t        /// &lt;param name=\&quot;app\&quot;&gt;&lt;/param&gt;\n    27\t        /// &lt;returns&gt;&lt;/returns&gt;\n    28\t        public static async Task&lt;IApplicationBuilder&gt; SeedDatabaseAsync(this IApplicationBuilder app)\n    29\t        {\n    30\t            using var scope = app.ApplicationServices.CreateScope();\n    31\t            var context = scope.ServiceProvider.GetRequiredService&lt;ApplicationDbContext&gt;();\n    32\t            \n    33\t            // 检查是否已有数据\n    34\t            if (!await context.Users.AnyAsync())\n    35\t            {\n    36\t                // 添加示例数据\n    37\t                var sampleUsers = new[]\n    38\t                {\n    39\t                    new Models.User { Name = \&quot;张三\&quot;, Email = \&quot;<EMAIL>\&quot; },\n    40\t                    new Models.User { Name = \&quot;李四\&quot;, Email = \&quot;<EMAIL>\&quot; },\n    41\t                    new Models.User { Name = \&quot;王五\&quot;, Email = \&quot;<EMAIL>\&quot; }\n    42\t                };\n    43\t\n    44\t                context.Users.AddRange(sampleUsers);\n    45\t                await context.SaveChangesAsync();\n    46\t            }\n    47\t            \n    48\t            return app;\n    49\t        }\n    50\t    }\n    51\t}\n...\nPath: docs/README_EFCore.md\n...\n    30\t\n    31\t- **开发环境**: `Data Source=simulator_dev.db`\n    32\t- **生产环境**: `Data Source=simulator.db`\n    33\t\n    34\t### 数据库上下文\n    35\t\n    36\t`ApplicationDbContext` 类位于 `Data/ApplicationDbContext.cs`，包含：\n    37\t- 实体配置\n    38\t- 关系映射\n    39\t- 约束定义\n    40\t\n    41\t## 使用示例\n    42\t\n    43\t### 1. 添加新实体\n    44\t\n    45\t1. 在 `Models/` 目录下创建实体类\n    46\t2. 在 `ApplicationDbContext` 中添加 `DbSet&lt;T&gt;` 属性\n    47\t3. 在 `OnModelCreating` 方法中配置实体关系\n    48\t4. 创建迁移...&quot;,&quot;isError&quot;:false,&quot;requestId&quot;:&quot;&quot;}},&quot;c2690f7a-955f-4a79-a0f3-4baa6723d422;toolu_01DN1FtLCx4nddUkhSnJzZFS&quot;:{&quot;requestId&quot;:&quot;c2690f7a-955f-4a79-a0f3-4baa6723d422&quot;,&quot;toolUseId&quot;:&quot;toolu_01DN1FtLCx4nddUkhSnJzZFS&quot;,&quot;phase&quot;:5,&quot;result&quot;:{&quot;text&quot;:&quot;- [Repository | FreeSql](https://freesql.net/en/guide/repository.html)\n  Jul 26, 2024 ... Insert data, optimized execution with ExecuteAffrows/ExecuteIdentity/ExecuteInserted ... //If the ServerTime property exists in the Column ...\n\n- [Insert Data · dotnetcore/FreeSql Wiki · GitHub](https://github.com/dotnetcore/FreeSql/wiki/Insert-Data)\n  Dec 14, 2023 ... If the table has auto-increment columns, id will be returned after inserting data. ... To execute BulkCopy to import data in batches, you need to ...\n\n- [Insert or Update | FreeSql](https://freesql.net/en/guide/insert-or-update.html)\n  Aug 21, 2021 ... ... attribute of the ... perform insert or update operations (batch supported). class TestInfo { [Column(IsIdentity = true)] public int id ...\n\n- [Repository Layer · dotnetcore/FreeSql Wiki · GitHub](https://github.com/dotnetcore/FreeSql/wiki/Repository-layer)\n  Jan 28, 2024 ... ... execution ExecuteAffrows , ExecuteIdentity or ExecuteInserted ; ... attributes, the batch insertion of the repository will be executed one by one.\n\n- [Getting Started · dotnetcore/FreeSql Wiki · GitHub](https://github.com/dotnetcore/FreeSql/wiki/Getting-Started)\n  Nov 22, 2023 ... Models. FreeSql uses a model to perform data access. The model is represented by an entity class to represent a database table or ...&quot;,&quot;isError&quot;:false,&quot;requestId&quot;:&quot;&quot;}},&quot;6d936d91-a25b-410f-8c89-135df7e094c3;toolu_011mxat4LGz4UvpKT7D1T8tm&quot;:{&quot;requestId&quot;:&quot;6d936d91-a25b-410f-8c89-135df7e094c3&quot;,&quot;toolUseId&quot;:&quot;toolu_011mxat4LGz4UvpKT7D1T8tm&quot;,&quot;phase&quot;:5,&quot;result&quot;:{&quot;text&quot;:&quot;                 Insert Data · dotnetcore/FreeSql Wiki · GitHub                                          \n\n[Skip to content](#start-of-content)   \n\nNavigation Menu\n---------------\n\nToggle navigation\n\n[](/)\n\n[Sign in](/login?return_to=https%3A%2F%2Fgithub.com%2Fdotnetcore%2FFreeSql%2Fwiki%2FInsert-Data)\n\nAppearance settings\n\n*   Product\n    \n    *   [\n        \n        GitHub Copilot\n        \n        Write better code with AI\n        \n        ](https://github.com/features/copilot)\n    *   [\n        \n        GitHub Models New\n        \n        Manage and compare prompts\n        \n        ](https://github.com/features/models)\n    *   [\n        \n        GitHub Advanced Security\n        \n        Find and fix vulnerabilities\n        \n        ](https://github.com/security/advanced-security)\n    *   [\n        \n        Actions\n        \n        Automate any workflow\n        \n        ](https://github.com/features/actions)\n    *   [\n        \n        Codespaces\n        \n        Instant dev environments\n        \n        ](https://github.com/features/codespaces)\n    \n    *   [\n        \n        Issues\n        \n        Plan and track work\n        \n        ](https://github.com/features/issues)\n    *   [\n        \n        Code Review\n        \n        Manage code changes\n        \n        ](https://github.com/features/code-review)\n    *   [\n        \n        Discussions\n        \n        Collaborate outside of code\n        \n        ](https://github.com/features/discussions)\n    *   [\n        \n        Code Search\n        \n        Find more, search less\n        \n        ](https://github.com/features/code-search)\n    \n    Explore\n    \n    *   [Why GitHub](https://github.com/why-github)\n    *   [All features](https://github.com/features)\n    *   [Documentation](https://docs.github.com)\n    *   [GitHub Skills](https://skills.github.com)\n    *   [Blog](https://github.blog)\n    \n*   Solutions\n    \n    By company size\n    \n    *   [Enterprises](https://github.com/enterprise)\n    *   [Small and medium teams](https://github.com/team)\n    *   [Startups](https://github.com/enterprise/startups)\n    *   [Nonprofits](/solutions/industry/nonprofits)\n    \n    By use case\n    \n    *   [DevSecOps](/solutions/use-case/devsecops)\n    *   [DevOps](/solutions/use-case/devops)\n    *   [CI/CD](/solutions/use-case/ci-cd)\n    *   [View all use cases](/solutions/use-case)\n    \n    By industry\n    \n    *   [Healthcare](/solutions/industry/healthcare)\n    *   [Financial services](/solutions/industry/financial-services)\n    *   [Manufacturing](/solutions/industry/manufacturing)\n    *   [Government](/solutions/industry/government)\n    *   [View all industries](/solutions/industry)\n    \n    [View all solutions](/solutions)\n    \n*   Resources\n    \n    Topics\n    \n    *   [AI](/resources/articles/ai)\n    *   [DevOps](/resources/articles/devops)\n    *   [Security](/resources/articles/security)\n    *   [Software Development](/resources/articles/software-development)\n    *   [View all](/resources/articles)\n    \n    Explore\n    \n    *   [Learning Pathways](https://resources.github.com/learn/pathways)\n    *   [Events &amp; Webinars](https://resources.github.com)\n    *   [Ebooks &amp; Whitepapers](https://github.com/resources/whitepapers)\n    *   [Customer Stories](https://github.com/customer-stories)\n    *   [Partners](https://partner.github.com)\n    *   [Executive Insights](https://github.com/solutions/executive-insights)\n    \n*   Open Source\n    \n    *   [\n        \n        GitHub Sponsors\n        \n        Fund open source developers\n        \n        ](/sponsors)\n    \n    *   [\n        \n        The ReadME Project\n        \n        GitHub community articles\n        \n        ](https://github.com/readme)\n    \n    Repositories\n    \n    *   [Topics](https://github.com/topics)\n    *   [Trending](https://github.com/trending)\n    *   [Collections](https://github.com/collections)\n    \n*   Enterprise\n    \n    *   [\n        \n        Enterprise platform\n        \n        AI-powered developer platform\n        \n        ](/enterprise)\n    \n    Available add-ons\n    \n    *   [\n        \n        GitHub Advanced Security\n        \n        Enterprise-grade security features\n        \n        ](https://github.com/security/advanced-security)\n    *   [\n        \n        Copilot for business\n        \n        Enterprise-grade AI features\n        \n        ](/features/copilot/copilot-business)\n    *   [\n        \n        Premium Support\n        \n        Enterprise-grade 24/7 support\n        \n        ](/premium-support)\n    \n*   [Pricing](https://github.com/pricing)\n\nSearch or jump to...\n\nSearch code, repositories, users, issues, pull requests...\n==========================================================\n\nSearch\n\nClear\n\n[Search syntax tips](https://docs.github.com/search-github/github-code-search/understanding-github-code-search-syntax)\n\nProvide feedback\n================\n\nWe read every piece of feedback, and take your input very seriously.\n\n Include my email address so I can be contacted\n\nCancel Submit feedback\n\nSaved searches\n==============\n\nUse saved searches to filter your results more quickly\n------------------------------------------------------\n\nName  \n\nQuery \n\nTo see all available qualifiers, see our [documentation](https://docs.github.com/search-github/github-code-search/understanding-github-code-search-syntax).\n\nCancel Create saved search\n\n[Sign in](/login?return_to=https%3A%2F%2Fgithub.com%2Fdotnetcore%2FFreeSql%2Fwiki%2FInsert-Data)\n\n[Sign up](/signup?ref_cta=Sign+up&amp;ref_loc=header+logged+out&amp;ref_page=%2F%3Cuser-name%3E%2F%3Crepo-name%3E%2Fwiki%2Fshow&amp;source=header-repo&amp;source_repo=dotnetcore%2FFreeSql)\n\nAppearance settings\n\nResetting focus\n\nYou signed in with another tab or window. Reload to refresh your session. You signed out in another tab or window. Reload to refresh your session. You switched accounts on another tab or window. Reload to refresh your session. Dismiss alert\n\n[dotnetcore](/dotnetcore) / **[FreeSql](/dotnetcore/FreeSql)** Public\n\n*   [Notifications](/login?return_to=%2Fdotnetcore%2FFreeSql) You must be signed in to change notification settings\n*   [Fork 885](/login?return_to=%2Fdotnetcore%2FFreeSql)\n*   [Star 4.3k](/login?return_to=%2Fdotnetcore%2FFreeSql)\n    \n\n*   [Code](/dotnetcore/FreeSql)\n*   [Issues 72](/dotnetcore/FreeSql/issues)\n*   [Pull requests 7](/dotnetcore/FreeSql/pulls)\n*   [Discussions](/dotnetcore/FreeSql/discussions)\n*   [Actions](/dotnetcore/FreeSql/actions)\n*   [Projects 0](/dotnetcore/FreeSql/projects)\n*   [Models](/dotnetcore/FreeSql/models)\n*   [Wiki](/dotnetcore/FreeSql/wiki)\n*   [Security](/dotnetcore/FreeSql/security)\n    \n    [](/dotnetcore/FreeSql/security)\n    \n    [](/dotnetcore/FreeSql/security)\n    \n    [](/dotnetcore/FreeSql/security)\n    \n    [\n    \n    ### Uh oh!\n    \n    ](/dotnetcore/FreeSql/security)\n    \n    [There was an error while loading.](/dotnetcore/FreeSql/security) Please reload this page.\n    \n*   [Insights](/dotnetcore/FreeSql/pulse)\n\nAdditional navigation options\n\n*   [Code](/dotnetcore/FreeSql)\n*   [Issues](/dotnetcore/FreeSql/issues)\n*   [Pull requests](/dotnetcore/FreeSql/pulls)\n*   [Discussions](/dotnetcore/FreeSql/discussions)\n*   [Actions](/dotnetcore/FreeSql/actions)\n*   [Projects](/dotnetcore/FreeSql/projects)\n*   [Models](/dotnetcore/FreeSql/models)\n*   [Wiki](/dotnetcore/FreeSql/wiki)\n*   [Security](/dotnetcore/FreeSql/security)\n*   [Insights](/dotnetcore/FreeSql/pulse)\n\nInsert Data\n===========\n\n[Jump to bottom](#wiki-pages-box)\n\n2881099 edited this page Dec 14, 2023 · [15 revisions](/dotnetcore/FreeSql/wiki/Insert-Data/_history)\n\n[中文](%e6%b7%bb%e5%8a%a0) | **English**\n\nFreeSql provides methods for inserting data in single and batches, and it can also return the inserted records when executed in a specific database.\n\nstatic IFreeSql fsql \\= new FreeSql.FreeSqlBuilder()\n    .UseConnectionString(FreeSql.DataType.MySql, connectionString)\n    .UseAutoSyncStructure(true) //Automatically synchronize the entity structure to the database\n    .Build(); //Be sure to define as singleton mode\n\nclass Topic {\n    \\[Column(IsIdentity \\= true, IsPrimary \\= true)\\]\n    public int Id { get; set; }\n    public int Clicks { get; set; }\n    public string Title { get; set; }\n    public DateTime CreateTime { get; set; }\n}\n\nvar items \\= new List&lt;Topic\\&gt;();\nfor (var a \\= 0; a &lt; 10; a++) items.Add(new Topic { Title \\= $\&quot;newtitle{a}\&quot;, Clicks \\= a \\* 100 });\n\n1\\. Single Insert\n-----------------\n\n[](#1-single-insert)\n\nvar t1 \\= fsql.Insert(items\\[0\\]).ExecuteAffrows();\n//INSERT INTO \\`Topic\\`(\\`Clicks\\`, \\`Title\\`, \\`CreateTime\\`) \n//VALUES(@Clicks0, @Title0, @CreateTime0)\n\nIf the table has auto-increment columns, `id` will be returned after inserting data.\n\nMethod 1: (Original)\n\nlong id \\= fsql.Insert(items\\[0\\]).ExecuteIdentity();\nitems\\[0\\].Id \\= id;\n\nMethod 2: (depends on FreeSql.Repository)\n\nvar repo \\= fsql.GetRepository&lt;Topic\\&gt;();\nrepo.Insert(items\\[0\\]);\n\n&gt; In the internal implementation, after inserting the data, the self-incremental value will be assigned to `items[0].Id` (support batch insert backfill)\n\n&gt; DbFirst sequence: \\[Column(IsIdentity = true, InsertValueSql = \&quot;seqname.nextval\&quot;)\\]\n\n2\\. Batch Insert\n----------------\n\n[](#2-batch-insert)\n\nvar t2 \\= fsql.Insert(items).ExecuteAffrows();\n//INSERT INTO \\`Topic\\`(\\`Clicks\\`, \\`Title\\`, \\`CreateTime\\`) \n//VALUES(@Clicks0, @Title0, @CreateTime0), (@Clicks1, @Title1, @CreateTime1), \n//(@Clicks2, @Title2, @CreateTime2), (@Clicks3, @Title3, @CreateTime3), \n//(@Clicks4, @Title4, @CreateTime4), (@Clicks5, @Title5, @CreateTime5), \n//(@Clicks6, @Title6, @CreateTime6), (@Clicks7, @Title7, @CreateTime7), \n//(@Clicks8, @Title8, @CreateTime8), (@Clicks9, @Title9, @CreateTime9)\n\n&gt; The errors that are easily caused by adding SqlServer in batches have been resolved:\n&gt; \n&gt;     The incoming request has too many parameters. The server supports a maximum of 2100 parameters. Reduce the number of parameters and resend the request.\n&gt;     \n&gt; \n&gt; Principle: Split into multiple packages and execute them in transactions.\n\nWhen inserting large quantities of data, the internal logic is divided and executed in batches. The segmentation rules are as follows:\n\nQuantity\n\nSize of Parameters\n\nMySql\n\n5000\n\n3000\n\nPostgreSQL\n\n5000\n\n3000\n\nSqlServer\n\n1000\n\n2100\n\nOracle\n\n500\n\n999\n\nSqlite\n\n5000\n\n999\n\n&gt; Quantity: It is the size of each batch of division. For example, a batch of 10,000 pieces of data will be inserted into two batches when mysql is executed.  \n&gt; Size of Parameters: the size of the parameter size divided into each batch. For example, when inserting 10,000 pieces of data in batches, each row needs to use 5 parameterizations, which will be divided into 3000/5 for each batch when mysql is executed.\n\nAfter the execution of the split, when the external transaction is not provided, the internal transaction is opened to achieve insertion integrity. You can also set appropriate values through `BatchOptions`.\n\nFreeSql adapts to the use of parameterization and non-parameterization of each data type. It is recommended to turn off the parameterization function for batch insertion and use `.NoneParameter()` to execute it.\n\n3\\. BulkCopy\n------------\n\n[](#3-bulkcopy)\n\npackage name\n\nmethod\n\ndesc\n\nFreeSql.Provider.SqlServer\n\nExecuteSqlBulkCopy\n\nFreeSql.Provider.MySqlConnector\n\nExecuteMySqlBulkCopy\n\nFreeSql.Provider.Oracle\n\nExecuteOracleBulkCopy\n\nFreeSql.Provider.Dameng\n\nExecuteDmBulkCopy\n\n达梦\n\nFreeSql.Provider.PostgreSQL\n\nExecutePgCopy\n\nFreeSql.Provider.KingbaseES\n\nExecuteKdbCopy\n\n人大金仓\n\nbulk insert test reference (52 fields)\n\n18W\n\n1W\n\n5K\n\n2K\n\n1K\n\n500\n\n100\n\n50\n\nMySql 5.5 ExecuteAffrows\n\n38,481\n\n2,234\n\n1,136\n\n284\n\n239\n\n167\n\n66\n\n30\n\nMySql 5.5 ExecuteMySqlBulkCopy\n\n28,405\n\n1,142\n\n657\n\n451\n\n435\n\n592\n\n47\n\n22\n\nSqlServer Express ExecuteAffrows\n\n402,355\n\n24,847\n\n11,465\n\n4,971\n\n2,437\n\n915\n\n138\n\n88\n\nSqlServer Express ExecuteSqlBulkCopy\n\n21,065\n\n578\n\n326\n\n139\n\n105\n\n79\n\n60\n\n48\n\nPostgreSQL 10 ExecuteAffrows\n\n46,756\n\n3,294\n\n2,269\n\n1,019\n\n374\n\n209\n\n51\n\n37\n\nPostgreSQL 10 ExecutePgCopy\n\n10,090\n\n583\n\n337\n\n136\n\n88\n\n61\n\n30\n\n25\n\n&gt; Explanation of 8W: insert 180000 rows of records, and the number in the table is the execution time (unit: ms)\n\nbulk insert test reference (10 fields)\n\n18W\n\n1W\n\n5K\n\n2K\n\n1K\n\n500\n\n100\n\n50\n\nMySql 5.5 ExecuteAffrows\n\n11,171\n\n866\n\n366\n\n80\n\n83\n\n50\n\n24\n\n34\n\nMySql 5.5 ExecuteMySqlBulkCopy\n\n6,504\n\n399\n\n257\n\n116\n\n87\n\n100\n\n16\n\n16\n\nSqlServer Express ExecuteAffrows\n\n47,204\n\n2,275\n\n1,108\n\n488\n\n279\n\n123\n\n35\n\n16\n\nSqlServer Express ExecuteSqlBulkCopy\n\n4,248\n\n127\n\n71\n\n30\n\n48\n\n14\n\n11\n\n10\n\nPostgreSQL 10 ExecuteAffrows\n\n9,786\n\n568\n\n336\n\n157\n\n102\n\n34\n\n9\n\n6\n\nPostgreSQL 10 ExecutePgCopy\n\n4,081\n\n167\n\n93\n\n39\n\n21\n\n12\n\n4\n\n2\n\n&gt; The test results are all based on the same operating system, and all are preheated.\n\n4\\. Dynamic tablename\n---------------------\n\n[](#4-dynamic-tablename)\n\nfsql.Insert(items).AsTable(\&quot;Topic\\_201903\&quot;).ExecuteAffrows();\n\n5\\. Insert the specified columns\n--------------------------------\n\n[](#5-insert-the-specified-columns)\n\nvar t3 \\= fsql.Insert(items).InsertColumns(a \\=&gt; a.Title).ExecuteAffrows();\n//INSERT INTO \\`Topic\\`(\\`Title\\`) \n//VALUES(@Title0), (@Title1), (@Title2), (@Title3), (@Title4), \n//(@Title5), (@Title6), (@Title7), (@Title8), (@Title9)\n\nvar t4 \\= fsql.Insert(items).InsertColumns(a \\=&gt;new { a.Title, a.Clicks }).ExecuteAffrows();\n//INSERT INTO \\`Topic\\`(\\`Clicks\\`, \\`Title\\`) \n//VALUES(@Clicks0, @Title0), (@Clicks1, @Title1), (@Clicks2, @Title2), \n//(@Clicks3, @Title3), (@Clicks4, @Title4), (@Clicks5, @Title5), \n//(@Clicks6, @Title6), (@Clicks7, @Title7), (@Clicks8, @Title8), \n//(@Clicks9, @Title9)\n\n6\\. Ignore the specified columns\n--------------------------------\n\n[](#6-ignore-the-specified-columns)\n\nvar t5 \\= fsql.Insert(items).IgnoreColumns(a \\=&gt; a.CreateTime).ExecuteAffrows();\n//INSERT INTO \\`Topic\\`(\\`Clicks\\`, \\`Title\\`) \n//VALUES(@Clicks0, @Title0), (@Clicks1, @Title1), (@Clicks2, @Title2), \n//(@Clicks3, @Title3), (@Clicks4, @Title4), (@Clicks5, @Title5), \n//(@Clicks6, @Title6), (@Clicks7, @Title7), (@Clicks8, @Title8), \n//(@Clicks9, @Title9)\n\nvar t6 \\= fsql.Insert(items).IgnoreColumns(a \\=&gt; new { a.Title, a.CreateTime }).ExecuteAffrows();\n///INSERT INTO \\`Topic\\`(\\`Clicks\\`) \n//VALUES(@Clicks0), (@Clicks1), (@Clicks2), (@Clicks3), (@Clicks4), \n//(@Clicks5), (@Clicks6), (@Clicks7), (@Clicks8), (@Clicks9)\n\n7\\. Column Insertion Priority\n-----------------------------\n\n[](#7-column-insertion-priority)\n\nAll Columns &lt; Specified columns (InsertColumns) &lt; Ignored Columns (IgnoreColumns)\n\n*   Without using `InsertColumns/IgnoreColumns`, all columns of the entity will be inserted into the database;\n*   Otherwise, when using `InsertColumns` and not using `IgnoreColumns`, only the specified columns are inserted into the database;\n*   Otherwise, in the case of using `IgnoreColumns`, only unspecified columns are inserted into the database.\n\n8、Dictionary Insert\n-------------------\n\n[](#8dictionary-insert)\n\nvar dic \\= new Dictionary&lt;string, object\\&gt;();\ndic.Add(\&quot;id\&quot;, 1);\ndic.Add(\&quot;name\&quot;, \&quot;xxxx\&quot;);\n\nfsql.InsertDict(dic).AsTable(\&quot;table1\&quot;).ExecuteAffrows();\n\n9\\. Import table data\n---------------------\n\n[](#9-import-table-data)\n\nint affrows \\= fsql.Select&lt;Topic\\&gt;()\n  .Limit(10)\n  .InsertInto(null, a \\=&gt; new Topic2\n  {\n    Title \\= a.Title\n  });\n\nINSERT INTO \\`Topic2\\`(\\`Title\\`, \\`Clicks\\`, \\`CreateTime\\`)\nSELECT a.\\`Title\\`, 0, '0001-01-01 00:00:00' \nFROM \\`Topic\\` a \nlimit 10\n\nNote: Because `Clicks` and `CreateTime` are not selected, it'll use the value set by the target entity attribute `[Column(InsertValueSql = xx)]`, or the default value of `C#` of the target entity attribute.\n\n10\\. `Insert Ignore Into` (MySql only)\n--------------------------------------\n\n[](#10-insert-ignore-into-mysql-only)\n\nfsql.Insert&lt;Topic\\&gt;().MySqlIgnoreInto().AppendData(items).ExecuteAffrows();\n///INSERT IGNORE INTO \\`Topic\\`(\\`Clicks\\`) \n//VALUES(@Clicks0), (@Clicks1), (@Clicks2), (@Clicks3), (@Clicks4), \n//(@Clicks5), (@Clicks6), (@Clicks7), (@Clicks8), (@Clicks9)\n\n11\\. `On Duplicate Key Update` (MySql only)\n-------------------------------------------\n\n[](#11-on-duplicate-key-update-mysql-only)\n\n[More information...](Insert-or-Update#6-on-duplicate-key-update-mysql-only)\n\n12\\. `On Conflict Do Update` (PostgreSQL only)\n----------------------------------------------\n\n[](#12-on-conflict-do-update-postgresql-only)\n\n[More information...](Insert-or-Update#7-on-conflict-do-update-postgresql-only)\n\nReference\n---------\n\n[](#reference)\n\n*   [《Database Transaction》](%e4%ba%8b%e5%8a%a1)\n*   [《FreeSql 101, Part 2: Delete Data》](Delete-Data)\n*   [《FreeSql 101, Part 3: Update Data》](Update-Data)\n*   [《FreeSql 101, Part 4: Query Data》](Query-Data)\n*   [《Repository Layer》](Repository-Layer)\n*   [《Filters and Global Filters》](Filters-and-Global-Filters)\n*   [《Sharding Tables and Database》](%e5%88%86%e8%a1%a8%e5%88%86%e5%ba%93)\n*   [《Tenant》](%e7%a7%9f%e6%88%b7)\n\nAPI\n---\n\n[](#api)\n\nMethods\n\nReturn\n\nParameters\n\nDescription\n\nAppendData\n\n&lt;this&gt;\n\nT1 | IEnumerable&lt;T1&gt;\n\nAppend the entity to be inserted\n\nInsertIdentity\n\n&lt;this&gt;\n\nNone\n\nSpecify the insert auto-increment column\n\nInsertColumns\n\n&lt;this&gt;\n\nLambda\n\nSpecify the inserted columns\n\nIgnoreColumns\n\n&lt;this&gt;\n\nLambda\n\nSpecify the ignored columns\n\nIgnoreInsertValueSql\n\n&lt;this&gt;\n\nLambda\n\nSpecify the ignored InsertValueSql columns\n\nCommandTimeout\n\n&lt;this&gt;\n\nint\n\nCommand timeout setting (seconds)\n\nWithTransaction\n\n&lt;this&gt;\n\nDbTransaction\n\nSet transaction object\n\nWithConnection\n\n&lt;this&gt;\n\nDbConnection\n\nSet the connection object\n\nToSql\n\nstring\n\nReturn the SQL statement to be executed.\n\n[OnDuplicateKeyUpdate](%e6%b7%bb%e5%8a%a0%e6%88%96%e4%bf%ae%e6%94%b9)\n\nOnDuplicateKeyUpdate&lt;T1&gt;\n\nNone\n\nMySql only. `On Duplicate Key Update`\n\n[OnConflictDoUpdate](%e6%b7%bb%e5%8a%a0%e6%88%96%e4%bf%ae%e6%94%b9)\n\nOnConflictDoUpdate&lt;T1&gt;\n\nNone\n\nPostgreSQL only. `On Conflict Do Update`\n\nExecuteAffrows\n\nlong\n\nExecute SQL and return the number of rows affected.\n\nExecuteIdentity\n\nlong\n\nExecute SQL and return the incremented value.\n\nExecuteInserted\n\nList&lt;T1&gt;\n\nExecute SQL and return the inserted records.\n\nExecuteSqlBulkCopy\n\nvoid\n\nSqlServer only. To execute BulkCopy to import data in batches, you need to reference `FreeSql.Provider.SqlServer`\n\nExecutePgCopy\n\nvoid\n\nPostgreSQL only. To execute BulkCopy to import data in batches, you need to reference `FreeSql.Provider.PostgreSQL`\n\nExecuteMySqlBulkCopy\n\nvoid\n\nMySql only. To execute BulkCopy to import data in batches, you need to reference `FreeSql.Provider.MysqlConnector`\n\nExecuteOracleBulkCopy\n\nvoid\n\nOracle only. To execute BulkCopy to import data in batches, you need to reference `FreeSql.Provider.Oracle`\n\nExecuteDmBulkCopy\n\nvoid\n\nDameng database only. To execute BulkCopy to import data in batches, you need to reference `FreeSql.Provider.Dameng`\n\n### Toggle table of contents Pages 79\n\n*   Loading\n    \n    [Home](/dotnetcore/FreeSql/wiki)\n    \n    ### Uh oh!\n    \n    There was an error while loading. Please reload this page.\n    \n*   Loading\n    \n    [ADO](/dotnetcore/FreeSql/wiki/ADO)\n    \n    ### Uh oh!\n    \n    There was an error while loading. Please reload this page.\n    \n*   Loading\n    \n    [AOP](/dotnetcore/FreeSql/wiki/AOP)\n    \n    ### Uh oh!\n    \n    There was an error while loading. Please reload this page.\n    \n*   Loading\n    \n    [API](/dotnetcore/FreeSql/wiki/API)\n    \n    ### Uh oh!\n    \n    There was an error while loading. Please reload this page.\n    \n*   Loading\n    \n    [BaseEntity](/dotnetcore/FreeSql/wiki/BaseEntity)\n    \n    ### Uh oh!\n    \n    There was an error while loading. Please reload this page.\n    \n*   Loading\n    \n    [Cascade Deletion](/dotnetcore/FreeSql/wiki/Cascade-Deletion)\n    \n    ### Uh oh!\n    \n    There was an error while loading. Please reload this page.\n    \n*   Loading\n    \n    [Cascade Saving](/dotnetcore/FreeSql/wiki/Cascade-Saving)\n    \n    ### Uh oh!\n    \n    There was an error while loading. Please reload this page.\n    \n*   Loading\n    \n    [CodeFirst](/dotnetcore/FreeSql/wiki/CodeFirst)\n    \n    ### Uh oh!\n    \n    There was an error while loading. Please reload this page.\n    \n*   Loading\n    \n    [Dapper比较](/dotnetcore/FreeSql/wiki/Dapper%E6%AF%94%E8%BE%83)\n    \n    ### Uh oh!\n    \n    There was an error while loading. Please reload this page.\n    \n*   Loading\n    \n    [DbContext](/dotnetcore/FreeSql/wiki/DbContext)\n    \n    ### Uh oh!\n    \n    There was an error while loading. Please reload this page.\n    \n*   Loading\n    \n    [DbFirst](/dotnetcore/FreeSql/wiki/DbFirst)\n    \n    ### Uh oh!\n    \n    There was an error while loading. Please reload this page.\n    \n*   Loading\n    \n    [Delete Data](/dotnetcore/FreeSql/wiki/Delete-Data)\n    \n    ### Uh oh!\n    \n    There was an error while loading. Please reload this page.\n    \n*   Loading\n    \n    [DI UnitOfWorkManager](/dotnetcore/FreeSql/wiki/DI-UnitOfWorkManager)\n    \n    ### Uh oh!\n    \n    There was an error while loading. Please reload this page.\n    \n*   Loading\n    \n    [Dynamic Operations](/dotnetcore/FreeSql/wiki/Dynamic-Operations)\n    \n    ### Uh oh!\n    \n    There was an error while loading. Please reload this page.\n    \n*   Loading\n    \n    [Entity Relationship](/dotnetcore/FreeSql/wiki/Entity-Relationship)\n    \n    ### Uh oh!\n    \n    There was an error while loading. Please reload this page.\n    \n*   Loading\n    \n    [EntityFramework比较](/dotnetcore/FreeSql/wiki/EntityFramework%E6%AF%94%E8%BE%83)\n    \n    ### Uh oh!\n    \n    There was an error while loading. Please reload this page.\n    \n*   Loading\n    \n    [FluentApi](/dotnetcore/FreeSql/wiki/FluentApi)\n    \n    ### Uh oh!\n    \n    There was an error while loading. Please reload this page.\n    \n*   Loading\n    \n    [Getting Started](/dotnetcore/FreeSql/wiki/Getting-Started)\n    \n    ### Uh oh!\n    \n    There was an error while loading. Please reload this page.\n    \n*   Loading\n    \n    [Greed Loading](/dotnetcore/FreeSql/wiki/Greed-Loading)\n    \n    ### Uh oh!\n    \n    There was an error while loading. Please reload this page.\n    \n*   Loading\n    \n    [Group Aggregation Query](/dotnetcore/FreeSql/wiki/Group-Aggregation-Query)\n    \n    ### Uh oh!\n    \n    There was an error while loading. Please reload this page.\n    \n*   Loading\n    \n    [Import Entity Configuration from Database](/dotnetcore/FreeSql/wiki/Import-Entity-Configuration-from-Database)\n    \n    ### Uh oh!\n    \n    There was an error while loading. Please reload this page.\n    \n*   Loading\n    \n    [Insert Data](/dotnetcore/FreeSql/wiki/Insert-Data)\n    \n    *   [1\\. Single Insert](/dotnetcore/FreeSql/wiki/Insert-Data#1-single-insert)\n    *   [2\\. Batch Insert](/dotnetcore/FreeSql/wiki/Insert-Data#2-batch-insert)\n    *   [3\\. BulkCopy](/dotnetcore/FreeSql/wiki/Insert-Data#3-bulkcopy)\n    *   [4\\. Dynamic tablename](/dotnetcore/FreeSql/wiki/Insert-Data#4-dynamic-tablename)\n    *   [5\\. Insert the specified columns](/dotnetcore/FreeSql/wiki/Insert-Data#5-insert-the-specified-columns)\n    *   [6\\. Ignore the specified columns](/dotnetcore/FreeSql/wiki/Insert-Data#6-ignore-the-specified-columns)\n    *   [7\\. Column Insertion Priority](/dotnetcore/FreeSql/wiki/Insert-Data#7-column-insertion-priority)\n    *   [8、Dictionary Insert](/dotnetcore/FreeSql/wiki/Insert-Data#8dictionary-insert)\n    *   [9\\. Import table data](/dotnetcore/FreeSql/wiki/Insert-Data#9-import-table-data)\n    *   [10\\. Insert Ignore Into (MySql only)](/dotnetcore/FreeSql/wiki/Insert-Data#10-insert-ignore-into-mysql-only)\n    *   [11\\. On Duplicate Key Update (MySql only)](/dotnetcore/FreeSql/wiki/Insert-Data#11-on-duplicate-key-update-mysql-only)\n    *   [12\\. On Conflict Do Update (PostgreSQL only)](/dotnetcore/FreeSql/wiki/Insert-Data#12-on-conflict-do-update-postgresql-only)\n    *   [Reference](/dotnetcore/FreeSql/wiki/Insert-Data#reference)\n    *   [API](/dotnetcore/FreeSql/wiki/Insert-Data#api)\n    \n*   Loading\n    \n    [Insert or Update](/dotnetcore/FreeSql/wiki/Insert-or-Update)\n    \n    ### Uh oh!\n    \n    There was an error while loading. Please reload this page.\n    \n*   Loading\n    \n    [Install](/dotnetcore/FreeSql/wiki/Install)\n    \n    ### Uh oh!\n    \n    There was an error while loading. Please reload this page.\n    \n*   Loading\n    \n    [Lazy Loading](/dotnetcore/FreeSql/wiki/Lazy-Loading)\n    \n    ### Uh oh!\n    \n    There was an error while loading. Please reload this page.\n    \n*   Loading\n    \n    [Linq to Sql](/dotnetcore/FreeSql/wiki/Linq-to-Sql)\n    \n    ### Uh oh!\n    \n    There was an error while loading. Please reload this page.\n    \n*   Loading\n    \n    [LinqToSql](/dotnetcore/FreeSql/wiki/LinqToSql)\n    \n    ### Uh oh!\n    \n    There was an error while loading. Please reload this page.\n    \n*   Loading\n    \n    [Nested Query](/dotnetcore/FreeSql/wiki/Nested-Query)\n    \n    ### Uh oh!\n    \n    There was an error while loading. Please reload this page.\n    \n*   Loading\n    \n    [Pagination](/dotnetcore/FreeSql/wiki/Pagination)\n    \n    ### Uh oh!\n    \n    There was an error while loading. Please reload this page.\n    \n*   Loading\n    \n    [Parent Child Relationship Query](/dotnetcore/FreeSql/wiki/Parent-Child-Relationship-Query)\n    \n    ### Uh oh!\n    \n    There was an error while loading. Please reload this page.\n    \n*   Loading\n    \n    [Query Data](/dotnetcore/FreeSql/wiki/Query-Data)\n    \n    ### Uh oh!\n    \n    There was an error while loading. Please reload this page.\n    \n*   Loading\n    \n    [Query from Multi Tables](/dotnetcore/FreeSql/wiki/Query-from-Multi-Tables)\n    \n    ### Uh oh!\n    \n    There was an error while loading. Please reload this page.\n    \n*   Loading\n    \n    [Query from Single Table](/dotnetcore/FreeSql/wiki/Query-from-Single-Table)\n    \n    ### Uh oh!\n    \n    There was an error while loading. Please reload this page.\n    \n*   Loading\n    \n    [Repository](/dotnetcore/FreeSql/wiki/Repository)\n    \n    ### Uh oh!\n    \n    There was an error while loading. Please reload this page.\n    \n*   Loading\n    \n    [Repository Layer](/dotnetcore/FreeSql/wiki/Repository-Layer)\n    \n    ### Uh oh!\n    \n    There was an error while loading. Please reload this page.\n    \n*   Loading\n    \n    [Return Data](/dotnetcore/FreeSql/wiki/Return-Data)\n    \n    ### Uh oh!\n    \n    There was an error while loading. Please reload this page.\n    \n*   Loading\n    \n    [Unit of Work](/dotnetcore/FreeSql/wiki/Unit-of-Work)\n    \n    ### Uh oh!\n    \n    There was an error while loading. Please reload this page.\n    \n*   Loading\n    \n    [Update Data](/dotnetcore/FreeSql/wiki/Update-Data)\n    \n    ### Uh oh!\n    \n    There was an error while loading. Please reload this page.\n    \n*   Loading\n    \n    [With Sql](/dotnetcore/FreeSql/wiki/With-Sql)\n    \n    ### Uh oh!\n    \n    There was an error while loading. Please reload this page.\n    \n*   Loading\n    \n    [withsql](/dotnetcore/FreeSql/wiki/withsql)\n    \n    ### Uh oh!\n    \n    There was an error while loading. Please reload this page.\n    \n*   Loading\n    \n    [事务](/dotnetcore/FreeSql/wiki/%E4%BA%8B%E5%8A%A1)\n    \n    ### Uh oh!\n    \n    There was an error while loading. Please reload this page.\n    \n*   Loading\n    \n    [修改](/dotnetcore/FreeSql/wiki/%E4%BF%AE%E6%94%B9)\n    \n    ### Uh oh!\n    \n    There was an error while loading. Please reload this page.\n    \n*   Loading\n    \n    [入门](/dotnetcore/FreeSql/wiki/%E5%85%A5%E9%97%A8)\n    \n    ### Uh oh!\n    \n    There was an error while loading. Please reload this page.\n    \n*   Loading\n    \n    [分组聚合查询](/dotnetcore/FreeSql/wiki/%E5%88%86%E7%BB%84%E8%81%9A%E5%90%88%E6%9F%A5%E8%AF%A2)\n    \n    ### Uh oh!\n    \n    There was an error while loading. Please reload this page.\n    \n*   Loading\n    \n    [分表分库](/dotnetcore/FreeSql/wiki/%E5%88%86%E8%A1%A8%E5%88%86%E5%BA%93)\n    \n    ### Uh oh!\n    \n    There was an error while loading. Please reload this page.\n    \n*   Loading\n    \n    [分页查询](/dotnetcore/FreeSql/wiki/%E5%88%86%E9%A1%B5%E6%9F%A5%E8%AF%A2)\n    \n    ### Uh oh!\n    \n    There was an error while loading. Please reload this page.\n    \n*   Loading\n    \n    [删除](/dotnetcore/FreeSql/wiki/%E5%88%A0%E9%99%A4)\n    \n    ### Uh oh!\n    \n    There was an error while loading. Please reload this page.\n    \n*   Loading\n    \n    [动态操作](/dotnetcore/FreeSql/wiki/%E5%8A%A8%E6%80%81%E6%93%8D%E4%BD%9C)\n    \n    ### Uh oh!\n    \n    There was an error while loading. Please reload this page.\n    \n*   Loading\n    \n    [单表查询](/dotnetcore/FreeSql/wiki/%E5%8D%95%E8%A1%A8%E6%9F%A5%E8%AF%A2)\n    \n    ### Uh oh!\n    \n    There was an error while loading. Please reload this page.\n    \n*   Loading\n    \n    [多表查询](/dotnetcore/FreeSql/wiki/%E5%A4%9A%E8%A1%A8%E6%9F%A5%E8%AF%A2)\n    \n    ### Uh oh!\n    \n    There was an error while loading. Please reload this page.\n    \n*   Loading\n    \n    [安装](/dotnetcore/FreeSql/wiki/%E5%AE%89%E8%A3%85)\n    \n    ### Uh oh!\n    \n    There was an error while loading. Please reload this page.\n    \n*   Loading\n    \n    [实体关系](/dotnetcore/FreeSql/wiki/%E5%AE%9E%E4%BD%93%E5%85%B3%E7%B3%BB)\n    \n    ### Uh oh!\n    \n    There was an error while loading. Please reload this page.\n    \n*   Loading\n    \n    [实体特性](/dotnetcore/FreeSql/wiki/%E5%AE%9E%E4%BD%93%E7%89%B9%E6%80%A7)\n    \n    ### Uh oh!\n    \n    There was an error while loading. Please reload this page.\n    \n*   Loading\n    \n    [导入数据库特性](/dotnetcore/FreeSql/wiki/%E5%AF%BC%E5%85%A5%E6%95%B0%E6%8D%AE%E5%BA%93%E7%89%B9%E6%80%A7)\n    \n    ### Uh oh!\n    \n    There was an error while loading. Please reload this page.\n    \n*   Loading\n    \n    [嵌套查询](/dotnetcore/FreeSql/wiki/%E5%B5%8C%E5%A5%97%E6%9F%A5%E8%AF%A2)\n    \n    ### Uh oh!\n    \n    There was an error while loading. Please reload this page.\n    \n*   Loading\n    \n    [工作单元](/dotnetcore/FreeSql/wiki/%E5%B7%A5%E4%BD%9C%E5%8D%95%E5%85%83)\n    \n    ### Uh oh!\n    \n    There was an error while loading. Please reload this page.\n    \n*   Loading\n    \n    [常见问题](/dotnetcore/FreeSql/wiki/%E5%B8%B8%E8%A7%81%E9%97%AE%E9%A2%98)\n    \n    ### Uh oh!\n    \n    There was an error while loading. Please reload this page.\n    \n*   Loading\n    \n    [延时加载](/dotnetcore/FreeSql/wiki/%E5%BB%B6%E6%97%B6%E5%8A%A0%E8%BD%BD)\n    \n    ### Uh oh!\n    \n    There was an error while loading. Please reload this page.\n    \n*   Loading\n    \n    [性能](/dotnetcore/FreeSql/wiki/%E6%80%A7%E8%83%BD)\n    \n    ### Uh oh!\n    \n    There was an error while loading. Please reload this page.\n    \n*   Loading\n    \n    [支持我们](/dotnetcore/FreeSql/wiki/%E6%94%AF%E6%8C%81%E6%88%91%E4%BB%AC)\n    \n    ### Uh oh!\n    \n    There was an error while loading. Please reload this page.\n    \n*   Loading\n    \n    [更新日志](/dotnetcore/FreeSql/wiki/%E6%9B%B4%E6%96%B0%E6%97%A5%E5%BF%97)\n    \n    ### Uh oh!\n    \n    There was an error while loading. Please reload this page.\n    \n*   Loading\n    \n    [查询](/dotnetcore/FreeSql/wiki/%E6%9F%A5%E8%AF%A2)\n    \n    ### Uh oh!\n    \n    There was an error while loading. Please reload this page.\n    \n*   Loading\n    \n    [查询父子关系](/dotnetcore/FreeSql/wiki/%E6%9F%A5%E8%AF%A2%E7%88%B6%E5%AD%90%E5%85%B3%E7%B3%BB)\n    \n    ### Uh oh!\n    \n    There was an error while loading. Please reload this page.\n    \n*   Loading\n    \n    [添加](/dotnetcore/FreeSql/wiki/%E6%B7%BB%E5%8A%A0)\n    \n    ### Uh oh!\n    \n    There was an error while loading. Please reload this page.\n    \n*   Loading\n    \n    [添加或修改](/dotnetcore/FreeSql/wiki/%E6%B7%BB%E5%8A%A0%E6%88%96%E4%BF%AE%E6%94%B9)\n    \n    ### Uh oh!\n    \n    There was an error while loading. Please reload this page.\n    \n*   Loading\n    \n    [租户](/dotnetcore/FreeSql/wiki/%E7%A7%9F%E6%88%B7)\n    \n    ### Uh oh!\n    \n    There was an error while loading. Please reload this page.\n    \n*   Loading\n    \n    [类型映射](/dotnetcore/FreeSql/wiki/%E7%B1%BB%E5%9E%8B%E6%98%A0%E5%B0%84)\n    \n    ### Uh oh!\n    \n    There was an error while loading. Please reload this page.\n    \n*   Loading\n    \n    [联合查询](/dotnetcore/FreeSql/wiki/%E8%81%94%E5%90%88%E6%9F%A5%E8%AF%A2)\n    \n    ### Uh oh!\n    \n    There was an error while loading. Please reload this page.\n    \n*   Loading\n    \n    [联级保存](/dotnetcore/FreeSql/wiki/%E8%81%94%E7%BA%A7%E4%BF%9D%E5%AD%98)\n    \n    ### Uh oh!\n    \n    There was an error while loading. Please reload this page.\n    \n*   Loading\n    \n    [联级删除](/dotnetcore/FreeSql/wiki/%E8%81%94%E7%BA%A7%E5%88%A0%E9%99%A4)\n    \n    ### Uh oh!\n    \n    There was an error while loading. Please reload this page.\n    \n*   Loading\n    \n    [聚合根（实验室）](/dotnetcore/FreeSql/wiki/%E8%81%9A%E5%90%88%E6%A0%B9%EF%BC%88%E5%AE%9E%E9%AA%8C%E5%AE%A4%EF%BC%89)\n    \n    ### Uh oh!\n    \n    There was an error while loading. Please reload this page.\n    \n*   Loading\n    \n    [自定义特性](/dotnetcore/FreeSql/wiki/%E8%87%AA%E5%AE%9A%E4%B9%89%E7%89%B9%E6%80%A7)\n    \n    ### Uh oh!\n    \n    There was an error while loading. Please reload this page.\n    \n*   Loading\n    \n    [表达式函数](/dotnetcore/FreeSql/wiki/%E8%A1%A8%E8%BE%BE%E5%BC%8F%E5%87%BD%E6%95%B0)\n    \n    ### Uh oh!\n    \n    There was an error while loading. Please reload this page.\n    \n*   Loading\n    \n    [读写分离](/dotnetcore/FreeSql/wiki/%E8%AF%BB%E5%86%99%E5%88%86%E7%A6%BB)\n    \n    ### Uh oh!\n    \n    There was an error while loading. Please reload this page.\n    \n*   Loading\n    \n    [贪婪加载](/dotnetcore/FreeSql/wiki/%E8%B4%AA%E5%A9%AA%E5%8A%A0%E8%BD%BD)\n    \n    ### Uh oh!\n    \n    There was an error while loading. Please reload this page.\n    \n*   Loading\n    \n    [过滤器](/dotnetcore/FreeSql/wiki/%E8%BF%87%E6%BB%A4%E5%99%A8)\n    \n    ### Uh oh!\n    \n    There was an error while loading. Please reload this page.\n    \n*   Loading\n    \n    [返回数据](/dotnetcore/FreeSql/wiki/%E8%BF%94%E5%9B%9E%E6%95%B0%E6%8D%AE)\n    \n    ### Uh oh!\n    \n    There was an error while loading. Please reload this page.\n    \n*   Loading\n    \n    [首页](/dotnetcore/FreeSql/wiki/%E9%A6%96%E9%A1%B5)\n    \n    ### Uh oh!\n    \n    There was an error while loading. Please reload this page.\n    \n*   Loading\n    \n    [骚操作](/dotnetcore/FreeSql/wiki/%E9%AA%9A%E6%93%8D%E4%BD%9C)\n    \n    ### Uh oh!\n    \n    There was an error while loading. Please reload this page.\n    \n*   Show 64 more pages…\n\nBasic\n-----\n\n[](#basic)\n\n*   [入门](%e5%85%a5%e9%97%a8) [Getting Started](Getting-Started)\n*   [安装](%e5%ae%89%e8%a3%85) [How to Install](Install)\n*   [添加](%e6%b7%bb%e5%8a%a0) [Insert Data](Insert-Data)\n    *   [单条](%e6%b7%bb%e5%8a%a0#1%E5%8D%95%E6%9D%A1%E6%8F%92%E5%85%A5) [Single](Insert-Data#1-single-insert)\n    *   [批量](%e6%b7%bb%e5%8a%a0#2%E6%89%B9%E9%87%8F%E6%8F%92%E5%85%A5) [Batch](Insert-Data#2-batch-insert)\n*   [删除](%e5%88%a0%e9%99%a4) [Delete Data](Delete-Data)\n*   [修改](%e4%bf%ae%e6%94%b9) [Update Data](Update-Data)\n    *   [更新实体](%e4%bf%ae%e6%94%b9#3%E6%9B%B4%E6%96%B0%E5%AE%9E%E4%BD%93) [Entity](Update-Data#3-update-the-entity)\n    *   [更新指定列](%e4%bf%ae%e6%94%b9#1%E6%9B%B4%E6%96%B0%E6%8C%87%E5%AE%9A%E5%88%97) [Specified Columns](Update-Data#1-update-the-specified-column)\n*   [添加或修改](%e6%b7%bb%e5%8a%a0%e6%88%96%e4%bf%ae%e6%94%b9) [Insert or Update](Insert-or-Update) ✨\n*   [查询](%e6%9f%a5%e8%af%a2) [Query Data](Query-Data)\n    *   [分页](%e5%88%86%e9%a1%b5%e6%9f%a5%e8%af%a2) [Pagination](Pagination)\n    *   [单表](%e5%8d%95%e8%a1%a8%e6%9f%a5%e8%af%a2) [Single Table](Query-from-Single-Table)\n    *   [多表](%e5%a4%9a%e8%a1%a8%e6%9f%a5%e8%af%a2) [Multi Tables](Query-from-Multi-Tables)\n    *   [分组聚合](%e5%88%86%e7%bb%84%e8%81%9a%e5%90%88%e6%9f%a5%e8%af%a2) [Group Aggregation](Group-Aggregation-Query)\n    *   [嵌套查询](%e5%b5%8c%e5%a5%97%e6%9f%a5%e8%af%a2) [Nested Query](Nested-Query) ✨\n    *   [联合查询](%E8%81%94%E5%90%88%E6%9F%A5%E8%AF%A2)\n    *   [返回数据](%e8%bf%94%e5%9b%9e%e6%95%b0%e6%8d%ae) [Return Data](Return-Data) ✨\n    *   [延时加载](%e5%bb%b6%e6%97%b6%e5%8a%a0%e8%bd%bd) [Lazy Loading](Lazy-Loading)\n    *   [贪婪加载](%e8%b4%aa%e5%a9%aa%e5%8a%a0%e8%bd%bd) [Greed Loading](Greed-Loading) ✨\n    *   [LINQ 扩展](LinqToSql) [LinqToSql](Linq-to-Sql)\n    *   [树型查询](%e6%9f%a5%e8%af%a2%e7%88%b6%e5%ad%90%e5%85%b3%e7%b3%bb) [Parent-Child Relp.](Parent-Child-Relationship-Query) ✨\n*   [仓储层](Repository) [Repository Layer](Repository-Layer)\n    *   [工作单元](%e5%b7%a5%e4%bd%9c%e5%8d%95%e5%85%83) [Unit of Work](Unit-of-Work)\n    *   [联级保存](%e8%81%94%e7%ba%a7%e4%bf%9d%e5%ad%98) [Cascade Saving](Cascade-Saving)\n    *   [联级删除](%E8%81%94%E7%BA%A7%E5%88%A0%E9%99%A4) [Cascade Deletion](Cascade-Deletion)\n    *   [工作单元管理器](DI-UnitOfWorkManager) ✨\n    *   [聚合根（实验室）](%E8%81%9A%E5%90%88%E6%A0%B9%EF%BC%88%E5%AE%9E%E9%AA%8C%E5%AE%A4%EF%BC%89)\n*   [CodeFirst](CodeFirst)\n    *   [实体特性✨](%e5%ae%9e%e4%bd%93%e7%89%b9%e6%80%a7)\n    *   [FluentApi](FluentApi)\n    *   [自定义特性](%e8%87%aa%e5%ae%9a%e4%b9%89%e7%89%b9%e6%80%a7)\n    *   [类型映射](%e7%b1%bb%e5%9e%8b%e6%98%a0%e5%b0%84)\n    *   [导航属性](%e5%ae%9e%e4%bd%93%e5%85%b3%e7%b3%bb) [Entity Relationship](Entity-Relationship) ✨\n    *   [迁移结构](CodeFirst#%e8%bf%81%e7%a7%bb%e7%bb%93%e6%9e%84)\n*   [DbFirst](DbFirst)\n*   [表达式函数](%e8%a1%a8%e8%be%be%e5%bc%8f%e5%87%bd%e6%95%b0)\n    *   [字符串](%e8%a1%a8%e8%be%be%e5%bc%8f%e5%87%bd%e6%95%b0#%e5%ad%97%e7%ac%a6%e4%b8%b2)\n    *   [日期/时间](%e8%a1%a8%e8%be%be%e5%bc%8f%e5%87%bd%e6%95%b0#%e6%97%a5%e6%9c%9f)\n    *   [其他](%e8%a1%a8%e8%be%be%e5%bc%8f%e5%87%bd%e6%95%b0#%e8%a1%a8%e8%be%be%e5%bc%8f%e5%87%bd%e6%95%b0%e5%85%a8%e8%a7%88)\n    *   [自定义函数](%e8%a1%a8%e8%be%be%e5%bc%8f%e5%87%bd%e6%95%b0#%E8%87%AA%E5%AE%9A%E4%B9%89%E8%A7%A3%E6%9E%90)\n*   [事务](%e4%ba%8b%e5%8a%a1)\n*   [过滤器](%e8%bf%87%e6%bb%a4%e5%99%a8)\n*   [ADO](ADO)\n*   [AOP✨](AOP)\n*   [读写分离](%e8%af%bb%e5%86%99%e5%88%86%e7%a6%bb)\n*   [分表分库](%e5%88%86%e8%a1%a8%e5%88%86%e5%ba%93)\n*   [租户](%e7%a7%9f%e6%88%b7)\n*   [性能](%e6%80%a7%e8%83%bd)\n*   [动态操作](%E5%8A%A8%E6%80%81%E6%93%8D%E4%BD%9C) [Dynamic Operations](Dynamic-Operations)\n*   [_你不知道的功能✨_](%E9%AA%9A%E6%93%8D%E4%BD%9C)\n*   [API参考](API)\n\n使用指南\n----\n\n[](#使用指南)\n\n*   [Dapper比较](Dapper%e6%af%94%e8%be%83)\n*   [EntityFramework比较](EntityFramework%e6%af%94%e8%be%83)\n\n学习与交流\n-----\n\n[](#学习与交流)\n\n*   [常见问题](%e5%b8%b8%e8%a7%81%e9%97%ae%e9%a2%98)\n*   [提交Issues](https://github.com/dotnetcore/FreeSql/issues/new/choose)\n\n信息\n--\n\n[](#信息)\n\n*   [服务支持](%E6%94%AF%E6%8C%81%E6%88%91%E4%BB%AC)\n*   [更新日志](%e6%9b%b4%e6%96%b0%e6%97%a5%e5%bf%97)\n\n##### Clone this wiki locally\n\nFooter\n------\n\n[](https://github.com)© 2025 GitHub, Inc.\n\n### Footer navigation\n\n*   [Terms](https://docs.github.com/site-policy/github-terms/github-terms-of-service)\n*   [Privacy](https://docs.github.com/site-policy/privacy-policies/github-privacy-statement)\n*   [Security](https://github.com/security)\n*   [Status](https://www.githubstatus.com/)\n*   [Docs](https://docs.github.com/)\n*   [Contact](https://support.github.com?tags=dotcom-footer)\n*   Manage cookies\n*   Do not share my personal information\n\nYou can’t perform that action at this time.&quot;,&quot;isError&quot;:false,&quot;requestId&quot;:&quot;&quot;}}},&quot;draftExchange&quot;:{&quot;request_message&quot;:&quot;&quot;,&quot;rich_text_json_repr&quot;:{&quot;type&quot;:&quot;doc&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;paragraph&quot;}]},&quot;status&quot;:&quot;draft&quot;},&quot;requestIds&quot;:[],&quot;isPinned&quot;:false,&quot;isShareable&quot;:true,&quot;extraData&quot;:{&quot;isAgentConversation&quot;:true,&quot;hasDirtyEdits&quot;:false},&quot;personaType&quot;:0,&quot;rootTaskUuid&quot;:&quot;9ed4c596-2ce4-493b-8e4e-ba76d0ff0798&quot;}},&quot;agentExecutionMode&quot;:&quot;manual&quot;,&quot;isPanelCollapsed&quot;:true,&quot;displayedAnnouncements&quot;:[]}" />
      </map>
    </option>
  </component>
</project>