using System.ComponentModel.DataAnnotations;

namespace Simulator.Server.Models.Dtos;

public class RegisterRequest
{
    [Required(ErrorMessage = "用户名不能为空")]
    [StringLength(50, MinimumLength = 3, ErrorMessage = "用户名长度必须在3-50个字符之间")]
    public required string Username { get; set; }

    [Required(ErrorMessage = "密码不能为空")]
    [StringLength(100, MinimumLength = 6, ErrorMessage = "密码长度必须在6-100个字符之间")]
    public required string Password { get; set; }

    public bool IsTeacher { get; set; } = false;
}
