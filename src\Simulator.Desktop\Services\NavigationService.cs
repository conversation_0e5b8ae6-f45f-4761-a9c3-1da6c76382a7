﻿using System;
using Avalonia.Controls;
using Microsoft.Extensions.DependencyInjection;
using Simulator.Desktop.ViewModels;

namespace Simulator.Desktop.Services;

public class NavigationService(IServiceProvider serviceProvider)
{
    public Action<Control, object?>? NavigateHandler { get; set; }

    public void Navigate(Type type, object? parameter)
    {
        var page = (Control)Activator.CreateInstance(type)!;
        var name = page.GetType().FullName!.Replace("View", "ViewModel");
        var vmType = Type.GetType(name);
        if (vmType != null)
        {
            var vm = (ViewModelBase)ActivatorUtilities.CreateInstance(serviceProvider, vmType)!;
            page.DataContext = vm;
        }

        NavigateHandler?.Invoke(page, parameter);
    }
}