﻿<UserControl xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:vm="clr-namespace:Simulator.Desktop.ViewModels"
             mc:Ignorable="d" d:DesignWidth="800" d:DesignHeight="450"
             x:Class="Simulator.Desktop.Views.ExaminationLoginView" x:DataType="vm:ExaminationLoginViewModel">
    <Grid ColumnDefinitions="*,*" ColumnSpacing="24">
        <StackPanel Grid.Column="0" VerticalAlignment="Center" HorizontalAlignment="Right">
            <TextBlock Text="News" />
            <TextBlock Text="111" />
        </StackPanel>
        <TabControl Grid.Column="1" VerticalAlignment="Center" HorizontalAlignment="Left">
            <TabItem Header="学员端">
                <StackPanel Width="256" Spacing="4">
                    <TextBlock Text="Username" />
                    <TextBox />
                    <TextBlock Text="Password" />
                    <TextBox />
                    <Button Theme="{StaticResource SolidButton}" Content="Login" HorizontalAlignment="Stretch"
                            Command="{Binding LoginCommand}" />
                </StackPanel>
            </TabItem>
            <TabItem Header="教员端">
                <StackPanel Width="256" Spacing="4">
                    <TextBlock Text="Username" />
                    <TextBox />
                    <TextBlock Text="Password" />
                    <TextBox />
                    <Button Theme="{StaticResource SolidButton}" Content="Login" HorizontalAlignment="Stretch"
                            Command="{Binding LoginCommand}" />
                </StackPanel>
            </TabItem>
        </TabControl>
    </Grid>
</UserControl>