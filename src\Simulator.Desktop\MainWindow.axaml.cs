using System;
using System.Windows.Input;
using Avalonia;
using Avalonia.Controls;
using Avalonia.Interactivity;
using Simulator.Desktop.Views;

namespace Simulator.Desktop;

public partial class MainWindow : Window
{
    public required Action<Type, object?> NavigateHandler { get; init; }

    public static readonly DirectProperty<MainWindow, ICommand> NavigateCommandProperty =
        AvaloniaProperty.RegisterDirect<MainWindow, ICommand>(nameof(NavigateCommand), o => o.NavigateCommand,
            (o, v) => o.NavigateCommand = v);

    public ICommand NavigateCommand
    {
        get;
        set => SetAndRaise(NavigateCommandProperty, ref field, value);
    }


    public MainWindow()
    {
        InitializeComponent();
    }

    public void Navigate(Control page, object? parameter)
    {
        Frame!.Content = page;
    }

    protected override void OnLoaded(RoutedEventArgs e)
    {
        base.OnLoaded(e);

        NavigateHandler(typeof(PortalView), null);
    }
}