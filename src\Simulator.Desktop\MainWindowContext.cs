﻿using System;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using Simulator.Desktop.Services;

namespace Simulator.Desktop;

public partial class MainWindowContext(NavigationService navigation) : ObservableObject
{
    #region Commands

    [RelayCommand]
    private void Navigate(Type? page)
    {
        if (page != null) navigation.Navigate(page, null);
    }

    #endregion
}