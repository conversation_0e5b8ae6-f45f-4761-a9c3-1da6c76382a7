﻿<Project Sdk="Microsoft.NET.Sdk">
    <PropertyGroup>
        <OutputType>WinExe</OutputType>
        <TargetFramework>net9.0</TargetFramework>
        <Nullable>enable</Nullable>
        <BuiltInComInteropSupport>true</BuiltInComInteropSupport>
        <ApplicationManifest>app.manifest</ApplicationManifest>
        <AvaloniaUseCompiledBindingsByDefault>true</AvaloniaUseCompiledBindingsByDefault>
        <LangVersion>preview</LangVersion>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Avalonia" Version="11.3.1" />
        <PackageReference Include="Avalonia.Desktop" Version="11.3.1" />
        <PackageReference Include="Avalonia.Themes.Fluent" Version="11.3.1" />
        <PackageReference Include="Avalonia.Fonts.Inter" Version="11.3.1" />
        <!--Condition below is needed to remove Avalonia.Diagnostics package from build output in Release configuration.-->
        <PackageReference Include="Avalonia.Diagnostics" Version="11.3.1">
            <IncludeAssets Condition="'$(Configuration)' != 'Debug'">None</IncludeAssets>
            <PrivateAssets Condition="'$(Configuration)' != 'Debug'">All</PrivateAssets>
        </PackageReference>
        <PackageReference Include="CommunityToolkit.Mvvm" Version="8.4.0" />
        <PackageReference Include="IconPacks.Avalonia.Lucide" Version="1.0.0" />
        <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="9.0.6" />
        <PackageReference Include="Microsoft.Extensions.Hosting" Version="9.0.6" />
        <PackageReference Include="Refit" Version="8.0.0" />
        <PackageReference Include="Semi.Avalonia" Version="********" />
    </ItemGroup>

    <ItemGroup>
      <Folder Include="Models\" />
    </ItemGroup>
</Project>
