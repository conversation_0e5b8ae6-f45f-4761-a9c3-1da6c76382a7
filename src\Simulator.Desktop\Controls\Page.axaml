﻿<ResourceDictionary xmlns="https://github.com/avaloniaui"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:controls="clr-namespace:Simulator.Desktop.Controls">
    <ControlTheme x:Key="{x:Type controls:Page}" TargetType="controls:Page">
        <Setter Property="Template">
            <ControlTemplate TargetType="controls:Page">
                <ContentPresenter Content="{TemplateBinding Content}"
                                  ContentTemplate="{TemplateBinding ContentTemplate}" />
            </ControlTemplate>
        </Setter>
    </ControlTheme>
</ResourceDictionary>