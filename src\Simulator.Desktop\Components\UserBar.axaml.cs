﻿using System;
using System.Windows.Input;
using Avalonia;
using Avalonia.Controls;
using Avalonia.Controls.Primitives;
using Avalonia.Markup.Xaml;

namespace Simulator.Desktop.Components;

public partial class UserBar : UserControl
{
    public static readonly DirectProperty<UserBar, Type?> SelectedPageProperty =
        AvaloniaProperty.RegisterDirect<UserBar, Type?>(nameof(SelectedPage), o => o.SelectedPage,
            (o, v) => o.SelectedPage = v);

    public Type? SelectedPage
    {
        get;
        set => SetAndRaise(SelectedPageProperty, ref field, value);
    }

    public static readonly DirectProperty<UserBar, ICommand?> NavigateCommandProperty =
        AvaloniaProperty.RegisterDirect<UserBar, ICommand?>(nameof(NavigateCommand), o => o.NavigateCommand,
            (o, v) => o.NavigateCommand = v);

    public ICommand? NavigateCommand
    {
        get;
        set => SetAndRaise(NavigateCommandProperty, ref field, value);
    }


    public UserBar()
    {
        InitializeComponent();
    }

    private void SelectingItemsControl_OnSelectionChanged(object? sender, SelectionChangedEventArgs e)
    {
        if (SelectedPage != null && e.AddedItems is [TabStripItem { Tag: Type page }] && page != SelectedPage)
        {
            NavigateCommand?.Execute(page);
        }
    }
}