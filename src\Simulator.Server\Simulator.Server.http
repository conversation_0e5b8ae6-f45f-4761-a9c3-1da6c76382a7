@Address = https://localhost:5212

# 用户注册
POST {{Address}}/api/user/register
Content-Type: application/json

{
  "username": "testuser",
  "password": "password123",
  "isTeacher": false
}

###

# 用户登录
POST {{Address}}/api/user/login
Content-Type: application/json

{
  "username": "testuser",
  "password": "password123"
}

###

# 获取用户信息 (需要JWT令牌)
GET {{Address}}/api/user/profile
Authorization: Bearer YOUR_JWT_TOKEN_HERE

###

# 获取题目 (需要JWT令牌)
GET {{Address}}/api/question/query/test-id
Authorization: Bearer YOUR_JWT_TOKEN_HERE

###

# 原有的API测试
GET {{Address}}/api/simulation/fluid/valves/M1201
Accept: application/json

###

PATCH {{Address}}/api/simulation/fluid/valves/M1201
Accept: application/json
Content-Type: application/json

[
  {
    "op": "replace",
    "path": "/Percentage",
    "value": 0.5
  }
]
###