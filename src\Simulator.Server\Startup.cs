﻿using FreeSql;
using Mapster;

namespace Simulator.Server
{
    public static class Startup
    {
        public static void ConfigureServices(IServiceCollection services, IConfiguration configuration)
        {
            services.AddSingleton<IFreeSql>(_ => new FreeSqlBuilder()
                .UseAdoConnectionPool(true)
                .UseConnectionString(DataType.Sqlite, "Data Source=Simulator.db;Cache=Private")
                .UseAutoSyncStructure(true)
                .Build());

            services.AddMapster();
        }
    }
}