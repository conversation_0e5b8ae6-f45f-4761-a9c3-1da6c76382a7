﻿using FreeSql;
using Mapster;
using Simulator.Server.Services;

namespace Simulator.Server
{
    public static class Startup
    {
        public static void ConfigureServices(IServiceCollection services, IConfiguration configuration)
        {
            services.AddSingleton<IFreeSql>(_ => new FreeSqlBuilder()
                .UseAdoConnectionPool(true)
                .UseConnectionString(DataType.Sqlite, "Data Source=Simulator.db;Cache=Private")
                .UseAutoSyncStructure(true)
                .Build());

            services.AddMapster();

            // 注册JWT服务
            services.AddScoped<IJwtService, JwtService>();
        }
    }
}