﻿using System.Text.Json;
using Microsoft.AspNetCore.Mvc;
using Simulator.Server.Models;

namespace Simulator.Server.Controllers;

[ApiController]
[Route("api/[controller]")]
public class QuestionController(IFreeSql sql) : Controller
{
    [HttpGet("query/{id}")]
    public IActionResult GetQuestion(string id)
    {
        var question = sql.Select<Question>().Where(q => q.Id == id).ToOne();
        return question == null ? NotFound() : Ok(question);
    }

    [HttpPost("query-many")]
    public IActionResult GetQuestions([FromBody] string[] ids)
    {
        var questions = sql.Select<Question>().Where(x => ids.Contains(x.Id)).ToList();
        return Ok(questions);
    }
}