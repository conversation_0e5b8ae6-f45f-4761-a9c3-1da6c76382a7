<Window xmlns="https://github.com/avaloniaui"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:vm="clr-namespace:Simulator.Desktop"
        mc:Ignorable="d" d:DesignWidth="800" d:DesignHeight="450"
        x:Class="Simulator.Desktop.MainWindow" x:DataType="vm:MainWindowContext"
        Title="Simulator.Desktop" NavigateCommand="{Binding NavigateCommand}">
    <DockPanel>
        <ContentPresenter DockPanel.Dock="Top"
                          IsVisible="{Binding #Frame.((HeaderedContentControl)Content).Header,Converter={x:Static ObjectConverters.IsNotNull},FallbackValue={x:False}}"
                          Content="{Binding #Frame.((HeaderedContentControl)Content).Header,FallbackValue={x:Null}}"
                          ContentTemplate="{Binding #Frame.((HeaderedContentControl)Content).HeaderTemplate,FallbackValue={x:Null}}"
                          Margin="12" />
        <TransitioningContentControl Name="Frame" Margin="12" />
    </DockPanel>
</Window>