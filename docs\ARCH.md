# Architecture

描述了基于现有项目扩展下的架构设计。

## 现有条件

目前已有一个可以具有管道系统的客户端，具有模拟和指导甲醇从加注到供应的功能。
技术栈：
- .Net 9/C# 14
- WPF
- 宏架构设计
- 无第三方控件库

## 需求

从单机架构改为 C/S 架构，要求教师端和作为主控端能够为学员端下发指令并回收状态。
教师端可以具有创建考试，在考试中监控学员状态，回收考卷，对考生成绩做统计处理，除此以外的其他学生端功能。

为日后开发方便，尽可能在架构上将模块分离，让每个人只负责自己的模块做一条龙开发，避免互相混合开发以产生等待他人开发和查错的情况耽误进度。


## 架构设计

- 部署席位
    - 学员端：多机部署（典型：40）
    - 教师端：单机部署（典型：2）
    - 服务端：中控、单机部署，可以和教师端部署在同一台机器上
- 角色：
    - 学员：一个学员端同一时间对应一个学员
    - 教师：一个教师端同一时间对应一个教师
    - 教师端和学员端有不同的验证流程，在架构实现中不是不同角色（Role），而是不同的类别（Class）
        - 事实上只有教师是持久化角色，学生是临时角色，其存在与否由教师决定（通过导入学生名单）
- 服务目标：考场
    - 软件部署在特定教室的电脑上
    - 
- 通信协议
    - 学员端 <-> 服务端：Http long polling + Http restful
    - 服务端 <-> 教师端：Http long polling + Http restful
- 学员端：直接在原先的客户端上进行侧挂模块，作为旁控制器，日后无法维护但工期要求极短
    - 使用 Generic Host 作为侧挂主机，前台服务为 WPF 生命线，与 WPF 实际生命线进行绑定而非托管（侧挂的意思就是无法做到托管才侧挂）
- 服务端
    - 负责将信息传回

### 通信与接口

考虑到项目是有实时性需求的，例如教师端会有下发试卷以及准时交卷，查看学员进度等实时的操作，在 Websocket 和 Http 之间选择了 Http，原因如下：
- 为什么不用 Websocket：Websocket 是异步调用，任何事情一旦引入异步就会变得异常复杂
- 为什么不用 RPC：配置麻烦
- 为什么用 Http long polling：
    - 成本低：其他组件是用 Http 之上构建，那么引入 Http long polling 就是顺便的事
    - 负担低：本身是同步调用，对人的心智负担小
    - 能完成任务
    - 弊端：占用资源较多，但根据现有场景下这个开销比起其他组件完全可以忽略不计

接口使用 Restful 规范，没有特别的理由，只是要有个规范。

### 学生端

已有现有客户端，只需要把现有组件做一个远程状态的对接即可，答题部分也是现有的，数据源从本地储存换成远程服务器上拉取。

### 教师端

可以是一个单独的桌面程序，也可以直接嵌入在学生端程序里，前者便于开发，后者有助于共享资源，例如直接调用学生端的管道模拟实现 Demo 查看与学生端监视。
决定采用独立桌面程序的决定因素在于是否需要有实时 Demo 查看的功能，也就是学生操作的时候同步到教师端。
如果没有，那么完全可以将学生端和教师端做成两个程序，当教师需要模拟的时候也打开一份学生端程序即可。

能力：
- 创建考试场次
- 导入与管理学生信息
- 下发试卷
- 编辑试卷
- 回收试卷
- 监视学生状态
- （待定）查看学生 Demo
- 统计成绩
- 其他便于教师上课的功能

### 服务端

整个设计重头在这里。
咱们这个是特定领域（Specific Domain）所以这里采用了领域特定设计（Domain-Specific Design）：使用考试场次作为数据持久的上下文

这意味着：
- 学生信息存在于特定场次考试，由创建该考试的教师决定
- 服务端为考试场次服务，通过创建一场考试来开启一次服务周期（Service Lifecycle）
- 教师信息和学生信息位于持久化层，教师能够在任何时候登录，但是瞬时性的状态则都在考试上下文中
- 关于考试上下文共存：
    - 能够共存意味着一个部署单位（教室）里可以进行多场考试
    - 上下文之间需要有特定界限的隔离与共享，例如同一批老师但不同的考试或不同的教师开启了不同的考试

技术选型：
- Asp.Net Core
- Entity Framework Core + Sqlite
- Mapster
- Restful Web API + Areas
- JWT Bearer Authentication

### 教师端

如果和学生端用同一个客户端，只需要扩展学生端即可，但是除了查看 Demo 功能会受到便利其他功能都会有负面影响。

如果单独作为一个程序并有试卷编辑器功能，那么：
- .Net 9/C# 14
- 方案1：使用新技术框架
    - 主线：Avalonia
    - 导航：RxUI 或随便写几行就有了
    - 数据路由：DynamicData
    - 控件库：Semi
- 方案2：复用原先技术栈
    - 主线：WPF
    - 导航：Prism 或随便写几行就有了
    - 控件库：HandyControl
- MVVM：CommunityToolkit.Mvvm
- 数据路由：DynamicData
- DI：Microsoft.Extensions.DependencyInjection
- 服务框架：Microsoft.Extensions.Hosting
- API：Refit
- 通信：Http long polling + Http restful

整个架构就是按 MVVM 来，由于模式较为固定，故不赘述。

## 设计决断

1. 是否需要实时演示与 Demo Playback 功能，这意味着教师端要借用学生端的模拟模块，要把两个客户端做在一起，会在原先的代码高耦合下做的更加复杂
2. 是否需要一个教室同时进行不同教师不同考试或同一个教师不同考试
    - 前者意味着最小化上下文，这样会采用最常规的通用设计，也就是完全没有领域特定性，没法利用部署单位为教室这个特点来降低复杂性
    - 后者依旧使用基于考试场次的上下文设计，但会出现同时存在两个或多个上下文