# FreeSql 集成说明

本项目已成功集成 FreeSql 并配置使用 SQLite 数据库。

## 已安装的包

- `FreeSql` - FreeSql 核心包
- `FreeSql.Provider.Sqlite` - SQLite 数据库提供程序

## 项目结构

```
src/Simulator.Server/
├── Data/
│   ├── FreeSqlExtensions.cs         # FreeSql 配置和扩展方法
│   └── Question.cs                  # 其他实体模型
├── Models/
│   └── User.cs                      # 示例实体模型
├── Controllers/
│   └── UsersController.cs           # 示例 API 控制器
├── appsettings.json                 # 生产环境配置
├── appsettings.Development.json     # 开发环境配置
└── simulator_dev.db                 # SQLite 数据库文件（开发环境）
```

## 数据库配置

### 连接字符串

- **开发环境**: `Data Source=simulator_dev.db`
- **生产环境**: `Data Source=simulator.db`

### FreeSql 配置

FreeSql 配置位于 `Data/FreeSqlExtensions.cs`，包含：
- 数据库连接配置
- 自动同步结构设置
- SQL 监控配置

## 使用示例

### 1. 添加新实体

1. 在 `Models/` 目录下创建实体类
2. 使用 FreeSql 注解配置实体（如 `[Table]`, `[Column]` 等）
3. 在 `FreeSqlExtensions.cs` 的 `EnsureDatabaseCreatedAsync` 方法中添加 `freeSql.CodeFirst.SyncStructure<YourEntity>()`
4. FreeSql 会自动创建或更新数据库结构

### 2. 在控制器中使用

```csharp
[ApiController]
[Route("api/[controller]")]
public class YourController : ControllerBase
{
    private readonly IFreeSql _freeSql;

    public YourController(IFreeSql freeSql)
    {
        _freeSql = freeSql;
    }

    [HttpGet]
    public async Task<ActionResult<IEnumerable<YourEntity>>> Get()
    {
        var entities = await _freeSql.Select<YourEntity>().ToListAsync();
        return Ok(entities);
    }
}
```

### 3. 在服务中使用

```csharp
public class YourService
{
    private readonly IFreeSql _freeSql;

    public YourService(IFreeSql freeSql)
    {
        _freeSql = freeSql;
    }

    public async Task<List<YourEntity>> GetAllAsync()
    {
        return await _freeSql.Select<YourEntity>().ToListAsync();
    }
}
```

## FreeSql 常用操作

```csharp
// 查询
var users = await freeSql.Select<User>().ToListAsync();
var user = await freeSql.Select<User>().Where(u => u.Id == 1).FirstAsync();

// 插入
var newUser = new User { Name = "张三", Email = "<EMAIL>" };
await freeSql.Insert(newUser).ExecuteAffrowsAsync();

// 更新
await freeSql.Update<User>()
    .Set(u => u.Name, "李四")
    .Where(u => u.Id == 1)
    .ExecuteAffrowsAsync();

// 删除
await freeSql.Delete<User>().Where(u => u.Id == 1).ExecuteAffrowsAsync();

// 同步数据库结构
freeSql.CodeFirst.SyncStructure<User>();
```

## API 端点示例

应用程序包含一个示例 Users API：

- `GET /api/users` - 获取所有用户
- `GET /api/users/{id}` - 获取指定用户
- `POST /api/users` - 创建新用户
- `PUT /api/users/{id}` - 更新用户
- `DELETE /api/users/{id}` - 删除用户

## 数据库初始化

应用程序启动时会自动：
1. 同步数据库结构（创建或更新表）
2. 在开发环境中添加示例数据（如果数据库为空）

这通过 `Program.cs` 中的以下代码实现：

```csharp
await app.EnsureDatabaseCreatedAsync();
if (app.Environment.IsDevelopment())
{
    await app.SeedDatabaseAsync();
}
```

## 注意事项

1. SQLite 数据库文件会在项目根目录下创建
2. 开发环境和生产环境使用不同的数据库文件
3. 迁移文件应该提交到版本控制系统
4. 数据库文件可以添加到 `.gitignore` 中（如果不需要共享数据）
