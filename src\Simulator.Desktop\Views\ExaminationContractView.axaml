﻿<controls:Page xmlns="https://github.com/avaloniaui"
               xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
               xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
               xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
               xmlns:conponents="clr-namespace:Simulator.Desktop.Components"
               xmlns:controls="clr-namespace:Simulator.Desktop.Controls"
               xmlns:icons="https://github.com/MahApps/IconPacks.Avalonia"
               xmlns:desktop="clr-namespace:Simulator.Desktop"
               xmlns:vm="clr-namespace:Simulator.Desktop.ViewModels"
               xmlns:v="clr-namespace:Simulator.Desktop.Views"
               mc:Ignorable="d" d:DesignWidth="800" d:DesignHeight="450"
               x:Class="Simulator.Desktop.Views.ExaminationContractView" x:DataType="vm:ExaminationContractViewModel">
    <controls:Page.Header>
        <conponents:UserBar SelectedPage="{x:Type v:ExaminationContractView}" />
    </controls:Page.Header>
    <Border HorizontalAlignment="Left" VerticalAlignment="Top" Padding="12" BorderBrush="Black" BorderThickness="2"
            CornerRadius="12">
        <StackPanel>
            <StackPanel Orientation="Horizontal" Spacing="6">
                <icons:PackIconLucide Kind="File" Height="36" />
                <TextBlock Text="试卷1" FontSize="24" VerticalAlignment="Center" />
            </StackPanel>
            <TextBlock Text="共 15 题，总分 100 分" Foreground="{StaticResource SemiColorText2}" />
            <TextBlock Text="七天前，2025/06/01 截至" Foreground="{StaticResource SemiColorText2}" />
            <StackPanel HorizontalAlignment="Right" Orientation="Horizontal" Margin="6" Spacing="6">
                <TextBlock Text="从未开始" VerticalAlignment="Center" />
                <Button Content="继续答题" />
            </StackPanel>
        </StackPanel>
    </Border>
</controls:Page>