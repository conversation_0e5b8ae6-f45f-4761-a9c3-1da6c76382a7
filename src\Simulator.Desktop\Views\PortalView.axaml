﻿<UserControl xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:icons="https://github.com/MahApps/IconPacks.Avalonia"
             xmlns:vm="clr-namespace:Simulator.Desktop.ViewModels"
             xmlns:v="clr-namespace:Simulator.Desktop.Views"
             mc:Ignorable="d" d:DesignWidth="800" d:DesignHeight="450"
             x:Class="Simulator.Desktop.Views.PortalView" x:DataType="vm:PortalViewModel">
    <StackPanel VerticalAlignment="Center" Spacing="24">
        <TextBlock Text="OCU Simulator Menu" HorizontalAlignment="Center" FontSize="24" />
        <Grid ColumnDefinitions="*,*,*" ColumnSpacing="8" HorizontalAlignment="Center" VerticalAlignment="Center">
            <Button Grid.Column="0" Width="128" Command="{Binding NavigateCommand}"
                    CommandParameter="{x:Type v:SimulationView}">
                <StackPanel>
                    <icons:PackIconLucide Kind="Workflow" Height="24" HorizontalAlignment="Center" Margin="12" />
                    <TextBlock Text="Simulation" />
                </StackPanel>
            </Button>
            <Button Grid.Column="1" Width="128" Command="{Binding NavigateCommand}"
                    CommandParameter="{x:Type v:ExaminationLoginView}">
                <StackPanel>
                    <icons:PackIconLucide Kind="SquarePen" Height="24" HorizontalAlignment="Center" Margin="12" />
                    <TextBlock Text="Examination" />
                </StackPanel>
            </Button>
            <Button Grid.Column="2" Width="128" Command="{Binding NavigateCommand}"
                    CommandParameter="{x:Type v:AdministrationView}">
                <StackPanel>
                    <icons:PackIconLucide Kind="Settings" Height="24" HorizontalAlignment="Center" Margin="12" />
                    <TextBlock Text="Administration" />
                </StackPanel>
            </Button>
        </Grid>
    </StackPanel>
</UserControl>