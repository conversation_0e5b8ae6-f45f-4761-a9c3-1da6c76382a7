﻿using Microsoft.AspNetCore.Mvc;
using NanoidDotNet;
using Simulator.Server.Models;

namespace Simulator.Server.Controllers;

[ApiController]
[Route("api/[controller]")]
public class UserController(IFreeSql sql) : Controller
{
    [HttpPost("login")]
    public IActionResult Login(string username, string password)
    {
        var user = sql.Select<User>().Where(u => u.Username == username && u.Password == password).ToOne();
        if (user == null)
        {
            return Unauthorized();
        }

        return Ok(user);
    }

    [HttpPost("register")]
    public IActionResult Register(string username, string password, bool isTeacher)
    {
        var studeacher = new User
        {
            Id = Nanoid.Generate(),
            Username = username,
            Password = password,
            IsTeacher = isTeacher
        };

        sql.Insert(studeacher).ExecuteAffrows();

        return Ok(studeacher);
    }
}